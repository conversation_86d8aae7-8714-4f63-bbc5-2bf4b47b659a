// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: matryer

package domain

import (
	"context"
	"sync"

	"github.com/google/uuid"
)

// Ensure that MoqPasswordEncoder does implement PasswordEncoder.
// If this is not the case, regenerate this file with mockery.
var _ PasswordEncoder = &MoqPasswordEncoder{}

// MoqPasswordEncoder is a mock implementation of PasswordEncoder.
//
//	func TestSomethingThatUsesPasswordEncoder(t *testing.T) {
//
//		// make and configure a mocked PasswordEncoder
//		mockedPasswordEncoder := &MoqPasswordEncoder{
//			EncodeFunc: func(password string) (string, error) {
//				panic("mock out the Encode method")
//			},
//			VerifyFunc: func(password string, hashedPassword string) error {
//				panic("mock out the Verify method")
//			},
//		}
//
//		// use mockedPasswordEncoder in code that requires PasswordEncoder
//		// and then make assertions.
//
//	}
type MoqPasswordEncoder struct {
	// EncodeFunc mocks the Encode method.
	EncodeFunc func(password string) (string, error)

	// VerifyFunc mocks the Verify method.
	VerifyFunc func(password string, hashedPassword string) error

	// calls tracks calls to the methods.
	calls struct {
		// Encode holds details about calls to the Encode method.
		Encode []struct {
			// Password is the password argument value.
			Password string
		}
		// Verify holds details about calls to the Verify method.
		Verify []struct {
			// Password is the password argument value.
			Password string
			// Password is the hashedPassword argument value.
			HashedPassword string
		}
	}
	lockEncode sync.RWMutex
	lockVerify sync.RWMutex
}

// Encode calls EncodeFunc.
func (mock *MoqPasswordEncoder) Encode(password string) (string, error) {
	if mock.EncodeFunc == nil {
		panic("MoqPasswordEncoder.EncodeFunc: method is nil but PasswordEncoder.Encode was just called")
	}
	callInfo := struct {
		Password string
	}{
		Password: password,
	}
	mock.lockEncode.Lock()
	mock.calls.Encode = append(mock.calls.Encode, callInfo)
	mock.lockEncode.Unlock()
	return mock.EncodeFunc(password)
}

// EncodeCalls gets all the calls that were made to Encode.
// Check the length with:
//
//	len(mockedPasswordEncoder.EncodeCalls())
func (mock *MoqPasswordEncoder) EncodeCalls() []struct {
	Password string
} {
	var calls []struct {
		Password string
	}
	mock.lockEncode.RLock()
	calls = mock.calls.Encode
	mock.lockEncode.RUnlock()
	return calls
}

// Verify calls VerifyFunc.
func (mock *MoqPasswordEncoder) Verify(password string, hashedPassword string) error {
	if mock.VerifyFunc == nil {
		panic("MoqPasswordEncoder.VerifyFunc: method is nil but PasswordEncoder.Verify was just called")
	}
	callInfo := struct {
		Password       string
		HashedPassword string
	}{
		Password:       password,
		HashedPassword: hashedPassword,
	}
	mock.lockVerify.Lock()
	mock.calls.Verify = append(mock.calls.Verify, callInfo)
	mock.lockVerify.Unlock()
	return mock.VerifyFunc(password, hashedPassword)
}

// VerifyCalls gets all the calls that were made to Verify.
// Check the length with:
//
//	len(mockedPasswordEncoder.VerifyCalls())
func (mock *MoqPasswordEncoder) VerifyCalls() []struct {
	Password       string
	HashedPassword string
} {
	var calls []struct {
		Password       string
		HashedPassword string
	}
	mock.lockVerify.RLock()
	calls = mock.calls.Verify
	mock.lockVerify.RUnlock()
	return calls
}

// Ensure that MoqUserRepository does implement UserRepository.
// If this is not the case, regenerate this file with mockery.
var _ UserRepository = &MoqUserRepository{}

// MoqUserRepository is a mock implementation of UserRepository.
//
//	func TestSomethingThatUsesUserRepository(t *testing.T) {
//
//		// make and configure a mocked UserRepository
//		mockedUserRepository := &MoqUserRepository{
//			FindByIdFunc: func(ctx context.Context, id uuid.UUID) (*User, error) {
//				panic("mock out the FindById method")
//			},
//			FindByUsernameFunc: func(ctx context.Context, username string) (*User, error) {
//				panic("mock out the FindByUsername method")
//			},
//			SaveFunc: func(ctx context.Context, user *User) error {
//				panic("mock out the Save method")
//			},
//		}
//
//		// use mockedUserRepository in code that requires UserRepository
//		// and then make assertions.
//
//	}
type MoqUserRepository struct {
	// FindByIdFunc mocks the FindById method.
	FindByIdFunc func(ctx context.Context, id uuid.UUID) (*User, error)

	// FindByUsernameFunc mocks the FindByUsername method.
	FindByUsernameFunc func(ctx context.Context, username string) (*User, error)

	// SaveFunc mocks the Save method.
	SaveFunc func(ctx context.Context, user *User) error

	// calls tracks calls to the methods.
	calls struct {
		// FindById holds details about calls to the FindById method.
		FindById []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// ID is the id argument value.
			ID uuid.UUID
		}
		// FindByUsername holds details about calls to the FindByUsername method.
		FindByUsername []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// Username is the username argument value.
			Username string
		}
		// Save holds details about calls to the Save method.
		Save []struct {
			// Ctx is the ctx argument value.
			Ctx context.Context
			// User is the user argument value.
			User *User
		}
	}
	lockFindById       sync.RWMutex
	lockFindByUsername sync.RWMutex
	lockSave           sync.RWMutex
}

// FindById calls FindByIdFunc.
func (mock *MoqUserRepository) FindById(ctx context.Context, id uuid.UUID) (*User, error) {
	if mock.FindByIdFunc == nil {
		panic("MoqUserRepository.FindByIdFunc: method is nil but UserRepository.FindById was just called")
	}
	callInfo := struct {
		Ctx context.Context
		ID  uuid.UUID
	}{
		Ctx: ctx,
		ID:  id,
	}
	mock.lockFindById.Lock()
	mock.calls.FindById = append(mock.calls.FindById, callInfo)
	mock.lockFindById.Unlock()
	return mock.FindByIdFunc(ctx, id)
}

// FindByIdCalls gets all the calls that were made to FindById.
// Check the length with:
//
//	len(mockedUserRepository.FindByIdCalls())
func (mock *MoqUserRepository) FindByIdCalls() []struct {
	Ctx context.Context
	ID  uuid.UUID
} {
	var calls []struct {
		Ctx context.Context
		ID  uuid.UUID
	}
	mock.lockFindById.RLock()
	calls = mock.calls.FindById
	mock.lockFindById.RUnlock()
	return calls
}

// FindByUsername calls FindByUsernameFunc.
func (mock *MoqUserRepository) FindByUsername(ctx context.Context, username string) (*User, error) {
	if mock.FindByUsernameFunc == nil {
		panic("MoqUserRepository.FindByUsernameFunc: method is nil but UserRepository.FindByUsername was just called")
	}
	callInfo := struct {
		Ctx      context.Context
		Username string
	}{
		Ctx:      ctx,
		Username: username,
	}
	mock.lockFindByUsername.Lock()
	mock.calls.FindByUsername = append(mock.calls.FindByUsername, callInfo)
	mock.lockFindByUsername.Unlock()
	return mock.FindByUsernameFunc(ctx, username)
}

// FindByUsernameCalls gets all the calls that were made to FindByUsername.
// Check the length with:
//
//	len(mockedUserRepository.FindByUsernameCalls())
func (mock *MoqUserRepository) FindByUsernameCalls() []struct {
	Ctx      context.Context
	Username string
} {
	var calls []struct {
		Ctx      context.Context
		Username string
	}
	mock.lockFindByUsername.RLock()
	calls = mock.calls.FindByUsername
	mock.lockFindByUsername.RUnlock()
	return calls
}

// Save calls SaveFunc.
func (mock *MoqUserRepository) Save(ctx context.Context, user *User) error {
	if mock.SaveFunc == nil {
		panic("MoqUserRepository.SaveFunc: method is nil but UserRepository.Save was just called")
	}
	callInfo := struct {
		Ctx  context.Context
		User *User
	}{
		Ctx:  ctx,
		User: user,
	}
	mock.lockSave.Lock()
	mock.calls.Save = append(mock.calls.Save, callInfo)
	mock.lockSave.Unlock()
	return mock.SaveFunc(ctx, user)
}

// SaveCalls gets all the calls that were made to Save.
// Check the length with:
//
//	len(mockedUserRepository.SaveCalls())
func (mock *MoqUserRepository) SaveCalls() []struct {
	Ctx  context.Context
	User *User
} {
	var calls []struct {
		Ctx  context.Context
		User *User
	}
	mock.lockSave.RLock()
	calls = mock.calls.Save
	mock.lockSave.RUnlock()
	return calls
}
