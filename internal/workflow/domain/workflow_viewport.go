package domain

import "github.com/bytedance/sonic"

type WorkflowViewport struct {
	x, y, zoom float32
}

func NewWorkflowViewport(x, y, zoom float32) *WorkflowViewport {
	return &WorkflowViewport{
		x:    x,
		y:    y,
		zoom: zoom,
	}
}

func NewDefaultWorkflowViewport() *WorkflowViewport {
	return NewWorkflowViewport(0, 0, 1)
}

func ReconstructWorkflowViewport(jsonStr string) (*WorkflowViewport, error) {
	var viewport *WorkflowViewport
	err := sonic.Unmarshal([]byte(jsonStr), viewport)
	if err != nil {
		return nil, err
	}
	return viewport, nil
}

func (v WorkflowViewport) String() (string, error) {
	jsonStr, err := sonic.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(jsonStr), nil
}
