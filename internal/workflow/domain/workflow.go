package domain

import (
	basedomain "resflow/internal/common/domain"
	"time"

	"github.com/google/uuid"
)

type Workflow struct {
	basedomain.AggregateRoot
	name        string
	description string
	status      WorkflowStatus
	userId      uuid.UUID
	WorkflowMeta
	nodes []*WorkflowNode
	edges []*WorkflowEdge
}

func NewWorkflow(name, description string, status WorkflowStatus, userId uuid.UUID, icon *Icon, viewport *WorkflowViewport, nodes []*WorkflowNode, edges []*WorkflowEdge,
) (*Workflow, error) {
	if name == "" {
		return nil, ErrNameRequired
	}

	meta, err := NewWorkflowMeta(icon, viewport)
	if err != nil {
		return nil, err
	}

	return &Workflow{
		AggregateRoot: basedomain.NewAggregateRoot(uuid.New(), time.Now(), time.Now()),
		name:          name,
		description:   description,
		status:        status,
		userId:        userId,
		WorkflowMeta:  meta,
		nodes:         nodes,
		edges:         edges,
	}, nil
}

func ReconstructWorkflow(id uuid.UUID, createdAt, updatedAt time.Time, name, description string, status int, userId uuid.UUID, icon *Icon, viewport *WorkflowViewport, nodes []*WorkflowNode, edges []*WorkflowEdge,
) *Workflow {
	meta, _ := NewWorkflowMeta(icon, viewport)

	return &Workflow{
		AggregateRoot: basedomain.NewAggregateRoot(id, createdAt, updatedAt),
		name:          name,
		description:   description,
		status:        WorkflowStatus(status),
		userId:        userId,
		WorkflowMeta:  meta,
		nodes:         nodes,
		edges:         edges,
	}
}

func (w Workflow) Name() string {
	return w.name
}

func (w Workflow) Description() string {
	return w.description
}

func (w Workflow) Status() WorkflowStatus {
	return w.status
}

func (w Workflow) UserId() uuid.UUID {
	return w.userId
}

func (w Workflow) Meta() WorkflowMeta {
	return w.WorkflowMeta
}

func (w Workflow) Nodes() WorkflowNodes {
	return w.nodes
}

func (w Workflow) Edges() WorkflowEdges {
	return w.edges
}
