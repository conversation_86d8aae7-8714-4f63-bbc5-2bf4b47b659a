package domain

import (
	basedomain "resflow/internal/common/domain"
	"time"

	"github.com/google/uuid"
)

type Workflow struct {
	basedomain.AggregateRoot
	name        string
	description string
	status      WorkflowStatus
	userId      uuid.UUID
	WorkflowMeta
	nodes []*WorkflowNode
	edges []*WorkflowEdge
}

func NewWorkflow(name, description string, status WorkflowStatus, userId uuid.UUID, meta WorkflowMeta, nodes []*WorkflowNode, edges []*WorkflowEdge,
) (*Workflow, error) {
	return &Workflow{
		AggregateRoot: basedomain.NewAggregateRoot(uuid.New(), time.Now(), time.Now()),
		name:          name,
		description:   description,
		status:        status,
		userId:        userId,
		WorkflowMeta:  meta,
		nodes:         nodes,
		edges:         edges,
	}, nil
}
