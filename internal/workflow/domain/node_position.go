package domain

import "github.com/bytedance/sonic"

type NodePosition struct {
	x, y float32
}

func NewNodePosition(x, y float32) *NodePosition {
	return &NodePosition{
		x: x,
		y: y,
	}
}

func NewDefaultNodePosition() *NodePosition {
	return NewNodePosition(0, 0)
}

func ReconstructNodePosition(jsonStr string) (*NodePosition, error) {
	var position *NodePosition
	err := sonic.Unmarshal([]byte(jsonStr), position)
	if err != nil {
		return nil, err
	}
	return position, nil
}

func (p NodePosition) String() (string, error) {
	jsonBytes, err := sonic.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}
