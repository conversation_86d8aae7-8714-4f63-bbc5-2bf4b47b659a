package domain

import (
	"github.com/bytedance/sonic"
)

type ParamType string

const (
	ParamTypeString  ParamType = "string"
	ParamTypeNumber  ParamType = "number"
	ParamTypeBoolean ParamType = "boolean"
	ParamTypeObject  ParamType = "object"
	ParamTypeArray   ParamType = "array"
)

type NodeParam struct {
	id          string
	label       string
	description string
	_type       ParamType
	required    bool
	value       interface{}
}

type NodeParams []*NodeParam

func (p NodeParams) String() (string, error) {
	jsonBytes, err := sonic.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

func NewNodeParam(id, label, description string, _type ParamType, required bool, value interface{}) (*NodeParam, error) {
	if id == "" {
		return nil, ErrIDRequired
	}

	return &NodeParam{
		id:          id,
		label:       label,
		description: description,
		_type:       _type,
		required:    required,
		value:       value,
	}, nil
}

func ReconstructNodeParams(jsonStr string) (NodeParams, error) {
	var nodeParams NodeParams
	err := sonic.Unmarshal([]byte(jsonStr), &nodeParams)
	if err != nil {
		return nil, err
	}
	return nodeParams, nil
}

func ReconstructNodeParam(json map[string]interface{}) *NodeParam {
	return &NodeParam{
		id:          json["id"].(string),
		label:       json["label"].(string),
		description: json["description"].(string),
		_type:       ParamType(json["type"].(string)),
		required:    json["required"].(bool),
		value:       json["value"],
	}
}

func (p NodeParam) JSON() map[string]interface{} {
	return map[string]interface{}{
		"id":          p.id,
		"label":       p.label,
		"description": p.description,
		"type":        p._type,
		"required":    p.required,
		"value":       p.value,
	}
}
