package domain

type ParamType string

const (
	ParamTypeString  ParamType = "string"
	ParamTypeNumber  ParamType = "number"
	ParamTypeBoolean ParamType = "boolean"
	ParamTypeObject  ParamType = "object"
	ParamTypeArray   ParamType = "array"
)

type NodeParam struct {
	id          string
	label       string
	description string
	_type       ParamType
	required    bool
	value       interface{}
}

func NewNodeParam(id, label, description string, _type ParamType, required bool, value interface{}) (*NodeParam, error) {
	if id == "" {
		return nil, ErrIDRequired
	}

	return &NodeParam{
		id:          id,
		label:       label,
		description: description,
		_type:       _type,
		required:    required,
		value:       value,
	}, nil
}
