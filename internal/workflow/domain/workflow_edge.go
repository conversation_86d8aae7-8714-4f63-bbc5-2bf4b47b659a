package domain

import (
	"errors"
	basedomain "resflow/internal/common/domain"
	"time"

	"github.com/google/uuid"
)

type WorkflowEdge struct {
	basedomain.AggregateRoot
	fromNodeId string
	toNodeId   string
	fromPortId string
	toPortId   string
	_type      string
}

var (
	ErrFromNodeIdRequired = errors.New("from node id required")
	ErrToNodeIdRequired   = errors.New("to node id required")
	ErrFromPortIdRequired = errors.New("from port id required")
	ErrToPortIdRequired   = errors.New("to port id required")
)

func NewWorkflowEdge(fromNodeId, toNodeId, fromPortId, toPortId, _type string) (*WorkflowEdge, error) {
	if fromNodeId == "" {
		return nil, ErrFromNodeIdRequired
	}

	if toNodeId == "" {
		return nil, ErrToNodeIdRequired
	}

	if fromPortId == "" {
		return nil, ErrFromPortIdRequired
	}

	if toPortId == "" {
		return nil, ErrToPortIdRequired
	}

	return &WorkflowEdge{
		AggregateRoot: basedomain.NewAggregateRoot(uuid.New(), time.Now(), time.Now()),
		fromNodeId:    fromNodeId,
		toNodeId:      toNodeId,
		fromPortId:    fromPortId,
		toPortId:      toPortId,
		_type:         _type,
	}, nil
}
