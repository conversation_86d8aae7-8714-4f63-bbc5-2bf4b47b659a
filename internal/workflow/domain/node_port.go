package domain

import (
	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

type PortType string

const (
	PortTypeSource PortType = "source"
	PortTypeTarget PortType = "target"
)

type NodePort struct {
	id    uuid.UUID
	_type PortType
}

type NodePorts []*NodePort

func NewNodePort(id uuid.UUID, _type PortType) (*NodePort, error) {
	if id.String() == "" {
		return nil, ErrIDRequired
	}

	return &NodePort{
		id:    id,
		_type: _type,
	}, nil
}

func ReconstructNodePorts(jsonStr string) (NodePorts, error) {
	var nodePorts NodePorts
	err := sonic.Unmarshal([]byte(jsonStr), &nodePorts)
	if err != nil {
		return nil, err
	}
	return nodePorts, nil
}

func (p NodePorts) String() (string, error) {
	jsonBytes, err := sonic.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

func (p NodePort) ID() uuid.UUID {
	return p.id
}

func (p NodePort) Type() PortType {
	return p._type
}
