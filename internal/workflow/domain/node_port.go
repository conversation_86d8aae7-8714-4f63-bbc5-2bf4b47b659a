package domain

import (
	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

type PortType string

const (
	PortTypeSource PortType = "source"
	PortTypeTarget PortType = "target"
)

type NodePort struct {
	id    uuid.UUID
	_type PortType
}

type NodePorts []*NodePort

func (p NodePorts) String() (string, error) {
	jsonBytes, err := sonic.Marshal(p)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

func NewNodePort(id uuid.UUID, _type PortType) (*NodePort, error) {
	if id.String() == "" {
		return nil, ErrIDRequired
	}

	return &NodePort{
		id:    id,
		_type: _type,
	}, nil
}

func (p NodePort) JSON() map[string]interface{} {
	return map[string]interface{}{
		"id":   p.id,
		"type": p._type,
	}
}

func (p NodePort) ID() uuid.UUID {
	return p.id
}

func (p NodePort) Type() PortType {
	return p._type
}
