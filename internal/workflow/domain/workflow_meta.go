package domain

type WorkflowMeta struct {
	icon     *Icon
	viewport *WorkflowViewport
}

func NewWorkflowMeta(icon *Icon, viewport *WorkflowViewport) (WorkflowMeta, error) {
	if icon == nil {
		return WorkflowMeta{}, ErrIconRequired
	}

	if viewport == nil {
		viewport = NewDefaultWorkflowViewport()
	}

	return WorkflowMeta{
		icon:     icon,
		viewport: viewport,
	}, nil
}

func (wm WorkflowMeta) Icon() *Icon {
	return wm.icon
}

func (wm WorkflowMeta) Viewport() *WorkflowViewport {
	return wm.viewport
}
