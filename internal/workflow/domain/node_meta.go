package domain

import "errors"

type NodeMeta struct {
	icon     *Icon
	position *NodePosition
}

var (
	ErrPositionRequired = errors.New("position required")
)

func NewNodeMeta(icon *Icon, position *NodePosition) (NodeMeta, error) {
	if icon == nil {
		return NodeMeta{}, ErrIconRequired
	}

	if position == nil {
		return NodeMeta{}, ErrPositionRequired
	}

	return NodeMeta{
		icon:     icon,
		position: position,
	}, nil
}
