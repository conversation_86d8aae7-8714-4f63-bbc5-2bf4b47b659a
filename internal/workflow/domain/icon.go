package domain

import "errors"

type IconType string

var (
	IconTypeText  IconType = "text"
	IconTypeImage IconType = "image"
	IconTypeSVG   IconType = "svg"
)

type Icon struct {
	iconType        IconType
	backgroundColor string
	text            string
	imageUrl        string
	svg             string
}

const WhiteColor = "#ffffff"

var (
	ErrTextTooLong      = errors.New("text too long")
	ErrImageUrlRequired = errors.New("image url required")
	ErrSVGRequired      = errors.New("svg required")
)

func NewTextIcon(text string, backgroundColor string) (*Icon, error) {
	if len(text) > 1 {
		return nil, ErrTextTooLong
	}

	if backgroundColor == "" {
		backgroundColor = WhiteColor
	}

	return &Icon{
		iconType:        IconTypeText,
		text:            text,
		backgroundColor: backgroundColor,
	}, nil
}

func NewImageIcon(url string) (*Icon, error) {
	if url == "" {
		return nil, ErrImageUrlRequired
	}

	return &Icon{
		iconType: IconTypeImage,
		imageUrl: url,
	}, nil
}

func NewSVGIcon(svg string, backgroundColor string) (*Icon, error) {
	if svg == "" {
		return nil, ErrSVGRequired
	}

	if backgroundColor == "" {
		backgroundColor = WhiteColor
	}

	return &Icon{
		iconType:        IconTypeSVG,
		svg:             svg,
		backgroundColor: backgroundColor,
	}, nil
}
