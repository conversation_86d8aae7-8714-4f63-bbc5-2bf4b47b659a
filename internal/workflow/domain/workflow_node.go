package domain

import (
	"errors"
	basedomain "resflow/internal/common/domain"
	"time"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

type WorkflowNode struct {
	basedomain.AggregateRoot
	name          string
	description   string
	_type         string
	version       string
	pluginName    string
	pluginVersion string
	inputParams   NodeParams
	outputParams  NodeParams
	inputPorts    NodePorts
	outputPorts   NodePorts
	configData    map[string]interface{}
	NodeMeta
}

type WorkflowNodes []*WorkflowNode

var (
	ErrTypeRequired    = errors.New("type required")
	ErrVersionRequired = errors.New("version required")
)

func NewWorkflowNode(name, description, _type, version string, icon *Icon, position *NodePosition, pluginName, pluginVersion string, inputParams, outputParams []*NodeParam, inputPorts, outputPorts []*NodePort, configData map[string]interface{},
) (*WorkflowNode, error) {
	if name == "" {
		return nil, ErrNameRequired
	}

	if _type == "" {
		return nil, ErrTypeRequired
	}

	if version == "" {
		return nil, ErrVersionRequired
	}

	meta, err := NewNodeMeta(icon, position)
	if err != nil {
		return nil, err
	}

	return &WorkflowNode{
		AggregateRoot: basedomain.NewAggregateRoot(uuid.New(), time.Now(), time.Now()),
		name:          name,
		description:   description,
		_type:         _type,
		version:       version,
		pluginName:    pluginName,
		pluginVersion: pluginVersion,
		inputParams:   inputParams,
		outputParams:  outputParams,
		inputPorts:    inputPorts,
		outputPorts:   outputPorts,
		configData:    configData,
		NodeMeta:      meta,
	}, nil
}

func ReconstructWorkflowNode(id uuid.UUID, createdAt, updatedAt time.Time, name, description, _type, version string, icon *Icon, position *NodePosition, pluginName, pluginVersion string, inputParams, outputParams []*NodeParam, inputPorts, outputPorts []*NodePort, configData map[string]interface{},
) *WorkflowNode {
	meta, _ := NewNodeMeta(icon, position)

	return &WorkflowNode{
		AggregateRoot: basedomain.NewAggregateRoot(id, createdAt, updatedAt),
		name:          name,
		description:   description,
		_type:         _type,
		version:       version,
		pluginName:    pluginName,
		pluginVersion: pluginVersion,
		inputParams:   inputParams,
		outputParams:  outputParams,
		inputPorts:    inputPorts,
		outputPorts:   outputPorts,
		configData:    configData,
		NodeMeta:      meta,
	}
}

func ReconstructWorkflowNodes(jsonStr string) (WorkflowNodes, error) {
	var workflowNodes WorkflowNodes
	err := sonic.Unmarshal([]byte(jsonStr), &workflowNodes)
	if err != nil {
		return nil, err
	}
	return workflowNodes, nil
}

func (n WorkflowNode) Name() string {
	return n.name
}

func (n WorkflowNode) Description() string {
	return n.description
}

func (n WorkflowNode) Type() string {
	return n._type
}

func (n WorkflowNode) Version() string {
	return n.version
}

func (n WorkflowNode) PluginName() string {
	return n.pluginName
}

func (n WorkflowNode) PluginVersion() string {
	return n.pluginVersion
}

func (n WorkflowNode) InputParams() NodeParams {
	return n.inputParams
}

func (n WorkflowNode) OutputParams() NodeParams {
	return n.outputParams
}

func (n WorkflowNode) InputPorts() NodePorts {
	return n.inputPorts
}

func (n WorkflowNode) OutputPorts() NodePorts {
	return n.outputPorts
}

func (n WorkflowNode) ConfigData() map[string]interface{} {
	return n.configData
}

func (n WorkflowNode) ConfigDataString() (string, error) {
	jsonBytes, err := sonic.Marshal(n.configData)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

func (n WorkflowNode) Icon() *Icon {
	return n.icon
}

func (n WorkflowNode) Position() *NodePosition {
	return n.position
}
