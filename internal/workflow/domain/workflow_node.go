package domain

import (
	basedomain "resflow/internal/common/domain"
)

type WorkflowNode struct {
	basedomain.AggregateRoot
	name          string
	description   string
	_type         string
	version       string
	pluginName    string
	pluginVersion string
	inputParams   []*NodeParam
	outputParams  []*NodeParam
	inputPorts    []*NodePort
	outputPorts   []*NodePort
	configData    map[string]interface{}
	NodeMeta
}
