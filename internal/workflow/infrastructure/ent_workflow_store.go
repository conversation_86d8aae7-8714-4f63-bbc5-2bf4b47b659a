package infrastructure

import (
	"context"
	"resflow/ent"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"
	"resflow/internal/workflow/domain"

	"github.com/bytedance/sonic"
	"github.com/google/uuid"
)

type EntWorkflowStore struct {
	client *ent.Client
}

func NewEntWorkflowStore(client *ent.Client) *EntWorkflowStore {
	return &EntWorkflowStore{client: client}
}

func (s EntWorkflowStore) Save(ctx context.Context, workflowEntity *domain.Workflow) error {
	// 判断是否存在workflow
	workflowExists, err := s.client.Workflow.Query().Where(workflow.ID(workflowEntity.ID())).First(ctx)
	if err != nil {
		return err
	}

	icon := workflowEntity.Meta().Icon()
	viewport, err := workflowEntity.Viewport().String()
	if err != nil {
		return err
	}

	if workflowExists == nil {
		_, err = ent.TxFromContext(ctx).Workflow.Create().SetID(workflowEntity.ID()).SetName(workflowEntity.Name()).SetDescription(workflowEntity.Description()).SetStatus(workflowEntity.Status().Int()).SetUserID(workflowEntity.UserId()).SetIconType(icon.IconType().String()).SetIconBgColor(icon.BackgroundColor()).SetIconData(icon.IconData()).
			SetViewport(viewport).SetCreatedAt(workflowEntity.CreatedAt()).SetUpdatedAt(workflowEntity.UpdatedAt()).Save(ctx)
		if err != nil {
			return err
		}
	} else {
		_, err = ent.TxFromContext(ctx).Workflow.Update().Where(workflow.ID(workflowEntity.ID())).SetName(workflowEntity.Name()).SetDescription(workflowEntity.Description()).SetStatus(workflowEntity.Status().Int()).SetUserID(workflowEntity.UserId()).SetIconType(icon.IconType().String()).SetIconBgColor(icon.BackgroundColor()).SetIconData(icon.IconData()).
			SetViewport(viewport).SetUpdatedAt(workflowEntity.UpdatedAt()).Save(ctx)
		if err != nil {
			return err
		}
	}

	// 处理节点
	for _, workflowNodeEntity := range workflowEntity.Nodes() {
		// 判断节点是否存在
		nodeExists, err := s.client.WorkflowNode.Query().Where(workflownode.ID(workflowNodeEntity.ID())).First(ctx)
		if err != nil {
			return err
		}

		nodeIcon := workflowNodeEntity.Icon()
		inputParamsJSON, err := workflowNodeEntity.InputParams().String()
		if err != nil {
			return err
		}
		outputParamsJSON, err := workflowNodeEntity.OutputParams().String()
		if err != nil {
			return err
		}
		inputPortsJSON, err := workflowNodeEntity.InputPorts().String()
		if err != nil {
			return err
		}
		outputPortsJSON, err := workflowNodeEntity.OutputPorts().String()
		if err != nil {
			return err
		}
		positionJSON, err := workflowNodeEntity.Position().String()
		if err != nil {
			return err
		}
		configDataJSON, err := workflowNodeEntity.ConfigDataString()
		if err != nil {
			return err
		}

		if nodeExists == nil {
			_, err := ent.TxFromContext(ctx).WorkflowNode.Create().SetID(workflowNodeEntity.ID()).SetWorkflowID(workflowEntity.ID()).SetName(workflowNodeEntity.Name()).SetDescription(workflowNodeEntity.Description()).SetIconType(nodeIcon.IconType().String()).SetIconBgColor(nodeIcon.BackgroundColor()).SetIconData(nodeIcon.IconData()).SetType(workflowNodeEntity.Type()).SetVersion(workflowNodeEntity.Version()).SetPluginName(workflowNodeEntity.PluginName()).SetPluginVersion(workflowNodeEntity.PluginVersion()).SetInputParams(inputParamsJSON).SetOutputParams(outputParamsJSON).SetInputPorts(inputPortsJSON).SetOutputPorts(outputPortsJSON).SetPosition(positionJSON).SetConfigData(configDataJSON).SetCreatedAt(workflowNodeEntity.CreatedAt()).SetUpdatedAt(workflowNodeEntity.UpdatedAt()).Save(ctx)
			if err != nil {
				return err
			}
		} else {
			_, err := ent.TxFromContext(ctx).WorkflowNode.Update().Where(workflownode.ID(workflowNodeEntity.ID())).SetName(workflowNodeEntity.Name()).SetDescription(workflowNodeEntity.Description()).SetIconType(nodeIcon.IconType().String()).SetIconBgColor(nodeIcon.BackgroundColor()).SetIconData(nodeIcon.IconData()).SetType(workflowNodeEntity.Type()).SetVersion(workflowNodeEntity.Version()).SetPluginName(workflowNodeEntity.PluginName()).SetPluginVersion(workflowNodeEntity.PluginVersion()).SetInputParams(inputParamsJSON).SetOutputParams(outputParamsJSON).SetInputPorts(inputPortsJSON).SetOutputPorts(outputPortsJSON).SetPosition(positionJSON).SetConfigData(configDataJSON).SetUpdatedAt(workflowNodeEntity.UpdatedAt()).Save(ctx)
			if err != nil {
				return err
			}
		}
	}

	// 处理边
	for _, workflowEdgeEntity := range workflowEntity.Edges() {
		// 判断边是否存在
		edgeExists, err := s.client.WorkflowsEdge.Query().Where(workflowsedge.ID(workflowEdgeEntity.ID())).First(ctx)
		if err != nil {
			return err
		}

		if edgeExists == nil {
			_, err := ent.TxFromContext(ctx).WorkflowsEdge.Create().SetID(workflowEdgeEntity.ID()).SetWorkflowID(workflowEntity.ID()).SetFromNodeID(workflowEdgeEntity.FromNodeId()).SetToNodeID(workflowEdgeEntity.ToNodeId()).SetFromPortID(workflowEdgeEntity.FromPortId()).SetToPortID(workflowEdgeEntity.ToPortId()).SetType(workflowEdgeEntity.Type()).SetCreatedAt(workflowEdgeEntity.CreatedAt()).SetUpdatedAt(workflowEdgeEntity.UpdatedAt()).Save(ctx)
			if err != nil {
				return err
			}
		} else {
			_, err := ent.TxFromContext(ctx).WorkflowsEdge.Update().Where(workflowsedge.ID(workflowEdgeEntity.ID())).SetWorkflowID(workflowEntity.ID()).SetFromNodeID(workflowEdgeEntity.FromNodeId()).SetToNodeID(workflowEdgeEntity.ToNodeId()).SetFromPortID(workflowEdgeEntity.FromPortId()).SetToPortID(workflowEdgeEntity.ToPortId()).SetType(workflowEdgeEntity.Type()).SetUpdatedAt(workflowEdgeEntity.UpdatedAt()).Save(ctx)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s EntWorkflowStore) Find(ctx context.Context, id uuid.UUID) (*domain.Workflow, error) {
	workflowDO, err := s.client.Workflow.Query().WithNodes().WithWorkflowEdges().Where(workflow.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	if workflowDO == nil {
		return nil, nil
	}
	return WorkflowDoToWorkflowEntity(workflowDO)
}

func (s EntWorkflowStore) Remove(ctx context.Context, id uuid.UUID) error {
	_, err := ent.TxFromContext(ctx).WorkflowNode.Delete().Where(workflownode.WorkflowID(id)).Exec(ctx)
	if err != nil {
		return err
	}
	_, err = ent.TxFromContext(ctx).WorkflowsEdge.Delete().Where(workflowsedge.WorkflowID(id)).Exec(ctx)
	if err != nil {
		return err
	}
	_, err = ent.TxFromContext(ctx).Workflow.Delete().Where(workflow.ID(id)).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

func WorkflowDoToWorkflowEntity(workflowDo *ent.Workflow) (*domain.Workflow, error) {
	nodes := make(domain.WorkflowNodes, 0)
	for _, workflowNodeDo := range workflowDo.Edges.Nodes {
		workflowNodeEntity, err := WorkflowNodeDoToWorkflowNodeEntity(workflowNodeDo)
		if err != nil {
			return nil, err
		}
		nodes = append(nodes, workflowNodeEntity)
	}
	edges := make(domain.WorkflowEdges, 0)
	for _, workflowEdgeDo := range workflowDo.Edges.WorkflowEdges {
		workflowEdgeEntity, err := WorkflowEdgeDoToWorkflowEdgeEntity(workflowEdgeDo)
		if err != nil {
			return nil, err
		}
		edges = append(edges, workflowEdgeEntity)
	}
	icon := domain.ReconstructIcon(domain.IconType(workflowDo.IconType), workflowDo.IconBgColor, workflowDo.IconData)
	viewport, err := domain.ReconstructWorkflowViewport(workflowDo.Viewport)
	if err != nil {
		return nil, err
	}

	return domain.ReconstructWorkflow(
		workflowDo.ID,
		workflowDo.CreatedAt,
		workflowDo.UpdatedAt,
		workflowDo.Name,
		workflowDo.Description,
		workflowDo.Status,
		workflowDo.UserID,
		icon,
		viewport,
		nodes,
		edges,
	), nil
}

func WorkflowNodeDoToWorkflowNodeEntity(workflowNodeDo *ent.WorkflowNode) (*domain.WorkflowNode, error) {
	icon := domain.ReconstructIcon(domain.IconType(workflowNodeDo.IconType), workflowNodeDo.IconBgColor, workflowNodeDo.IconData)

	position, err := domain.ReconstructNodePosition(workflowNodeDo.Position)
	if err != nil {
		return nil, err
	}

	inputParams, err := domain.ReconstructNodeParams(workflowNodeDo.InputParams)
	if err != nil {
		return nil, err
	}
	outputParams, err := domain.ReconstructNodeParams(workflowNodeDo.OutputParams)
	if err != nil {
		return nil, err
	}
	inputPorts, err := domain.ReconstructNodePorts(workflowNodeDo.InputPorts)
	if err != nil {
		return nil, err
	}
	outputPorts, err := domain.ReconstructNodePorts(workflowNodeDo.OutputPorts)
	if err != nil {
		return nil, err
	}
	var configData map[string]interface{}
	err = sonic.Unmarshal([]byte(workflowNodeDo.ConfigData), &configData)
	if err != nil {
		return nil, err
	}

	return domain.ReconstructWorkflowNode(
		workflowNodeDo.ID,
		workflowNodeDo.CreatedAt,
		workflowNodeDo.UpdatedAt,
		workflowNodeDo.Name,
		workflowNodeDo.Description,
		workflowNodeDo.Type,
		workflowNodeDo.Version,
		icon,
		position,
		workflowNodeDo.PluginName,
		workflowNodeDo.PluginVersion,
		inputParams,
		outputParams,
		inputPorts,
		outputPorts,
		configData,
	), nil
}

func WorkflowEdgeDoToWorkflowEdgeEntity(workflowEdgeDo *ent.WorkflowsEdge) (*domain.WorkflowEdge, error) {
	return domain.NewWorkflowEdge(
		workflowEdgeDo.FromNodeID,
		workflowEdgeDo.ToNodeID,
		workflowEdgeDo.FromPortID,
		workflowEdgeDo.ToPortID,
		workflowEdgeDo.Type,
	)
}
