package infrastructure

import (
	"context"
	"resflow/database"
	"resflow/ent"
	"resflow/internal/workflow/domain"

	"github.com/google/uuid"
)

type EntWorkflowStore struct {
	client *ent.Client
}

func NewEntWorkflowStore(client *ent.Client) *EntWorkflowStore {
	return &EntWorkflowStore{client: client}
}

func (s EntWorkflowStore) Save(ctx context.Context, workflow *domain.Workflow) error {
	
}

func (s EntWorkflowStore) Find(ctx context.Context, id uuid.UUID) (*domain.Workflow, error) {

}

func (s EntWorkflowStore) Remove(ctx context.Context, id uuid.UUID) error {

}
