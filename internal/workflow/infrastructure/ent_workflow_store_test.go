package infrastructure

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"resflow/ent"
	"resflow/ent/enttest"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"
	"resflow/internal/workflow/domain"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	_ "github.com/mattn/go-sqlite3"
)

func TestEntWorkflowStore_Save(t *testing.T) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	// 准备测试数据
	workflowId := uuid.New()
	userId := uuid.New()
	nodeId1 := uuid.New()
	nodeId2 := uuid.New()
	edgeId := uuid.New()

	// 创建测试工作流节点
	nodes := []*domain.WorkflowNode{
		createTestWorkflowNode(nodeId1, "Node 1", "processor"),
		createTestWorkflowNode(nodeId2, "Node 2", "transformer"),
	}

	// 创建测试工作流边
	edges := []*domain.WorkflowEdge{
		createTestWorkflowEdge(edgeId, nodeId1, nodeId2),
	}

	// 创建测试工作流
	workflow := createTestWorkflow(workflowId, userId, "Test Workflow", nodes, edges)

	// 执行保存
	err := store.Save(ctx, workflow)
	require.NoError(t, err)

	// 验证工作流是否保存成功
	savedWorkflow, err := client.Workflow.Get(ctx, workflowId)
	require.NoError(t, err)
	assert.Equal(t, "Test Workflow", savedWorkflow.Name)
	assert.Equal(t, userId, savedWorkflow.UserID)

	// 验证节点是否保存成功
	savedNodes, err := client.WorkflowNode.Query().Where(workflownode.WorkflowID(workflowId)).All(ctx)
	require.NoError(t, err)
	assert.Len(t, savedNodes, 2)

	// 验证边是否保存成功
	savedEdges, err := client.WorkflowsEdge.Query().Where(workflowsedge.WorkflowID(workflowId)).All(ctx)
	require.NoError(t, err)
	assert.Len(t, savedEdges, 1)
}

func TestEntWorkflowStore_Find(t *testing.T) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	// 准备测试数据
	workflowId := uuid.New()
	userId := uuid.New()
	nodeId := uuid.New()
	edgeId := uuid.New()

	// 直接在数据库中创建测试数据
	_, err := client.Workflow.Create().
		SetID(workflowId).
		SetUserID(userId).
		SetName("Test Workflow").
		SetDescription("Test Description").
		SetStatus(1).
		SetIconType("icon").
		SetIconData("data").
		SetViewport(json.RawMessage(`{"x": 0, "y": 0, "zoom": 1}`)).
		Save(ctx)
	require.NoError(t, err)

	_, err = client.WorkflowNode.Create().
		SetID(nodeId).
		SetWorkflowID(workflowId).
		SetName("Test Node").
		SetDescription("Test Node Description").
		SetIconType("node-icon").
		SetIconData("node-data").
		SetType("processor").
		SetVersion("1.0.0").
		SetPluginName("test-plugin").
		SetPluginVersion("1.0.0").
		SetInputParams(json.RawMessage(`[]`)).
		SetOutputParams(json.RawMessage(`[]`)).
		SetInputPorts(json.RawMessage(`[]`)).
		SetOutputPorts(json.RawMessage(`[]`)).
		SetPosition(json.RawMessage(`{"x": 100, "y": 100}`)).
		Save(ctx)
	require.NoError(t, err)

	_, err = client.WorkflowsEdge.Create().
		SetID(edgeId).
		SetWorkflowID(workflowId).
		SetFromNodeID(nodeId).
		SetToNodeID(nodeId).
		SetFromPortID("output").
		SetToPortID("input").
		SetType("data").
		Save(ctx)
	require.NoError(t, err)

	// 执行查找
	workflow, err := store.Find(ctx, workflowId)
	require.NoError(t, err)
	require.NotNil(t, workflow)

	// 验证工作流属性
	assert.Equal(t, workflowId, workflow.ID())
	assert.Equal(t, "Test Workflow", workflow.Name())
	assert.Equal(t, "Test Description", workflow.Description())
	assert.Equal(t, userId, workflow.UserId())
}

func TestEntWorkflowStore_Find_NotFound(t *testing.T) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	// 查找不存在的工作流
	workflow, err := store.Find(ctx, uuid.New())
	require.NoError(t, err)
	assert.Nil(t, workflow)
}

func TestEntWorkflowStore_Remove(t *testing.T) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	// 准备测试数据
	workflowId := uuid.New()
	userId := uuid.New()
	nodeId := uuid.New()
	edgeId := uuid.New()

	// 创建测试数据
	_, err := client.Workflow.Create().
		SetID(workflowId).
		SetUserID(userId).
		SetName("Test Workflow").
		SetDescription("Test Description").
		SetStatus(1).
		SetIconType("icon").
		SetIconData("data").
		SetViewport(json.RawMessage(`{"x": 0, "y": 0, "zoom": 1}`)).
		Save(ctx)
	require.NoError(t, err)

	_, err = client.WorkflowNode.Create().
		SetID(nodeId).
		SetWorkflowID(workflowId).
		SetName("Test Node").
		SetDescription("Test Node Description").
		SetIconType("node-icon").
		SetIconData("node-data").
		SetType("processor").
		SetVersion("1.0.0").
		SetPluginName("test-plugin").
		SetPluginVersion("1.0.0").
		SetInputParams(json.RawMessage(`[]`)).
		SetOutputParams(json.RawMessage(`[]`)).
		SetInputPorts(json.RawMessage(`[]`)).
		SetOutputPorts(json.RawMessage(`[]`)).
		SetPosition(json.RawMessage(`{"x": 100, "y": 100}`)).
		Save(ctx)
	require.NoError(t, err)

	_, err = client.WorkflowsEdge.Create().
		SetID(edgeId).
		SetWorkflowID(workflowId).
		SetFromNodeID(nodeId).
		SetToNodeID(nodeId).
		SetFromPortID("output").
		SetToPortID("input").
		SetType("data").
		Save(ctx)
	require.NoError(t, err)

	// 执行删除
	err = store.Remove(ctx, workflowId)
	require.NoError(t, err)

	// 验证工作流已被删除
	_, err = client.Workflow.Get(ctx, workflowId)
	assert.Error(t, err) // 应该返回NotFound错误

	// 验证节点已被删除
	nodeCount, err := client.WorkflowNode.Query().Where(workflownode.WorkflowID(workflowId)).Count(ctx)
	require.NoError(t, err)
	assert.Equal(t, 0, nodeCount)

	// 验证边已被删除
	edgeCount, err := client.WorkflowsEdge.Query().Where(workflowsedge.WorkflowID(workflowId)).Count(ctx)
	require.NoError(t, err)
	assert.Equal(t, 0, edgeCount)
}

// 辅助函数：创建测试工作流
func createTestWorkflow(id, userId uuid.UUID, name string, nodes []*domain.WorkflowNode, edges []*domain.WorkflowEdge) *domain.Workflow {
	meta := domain.WorkflowMeta{
		IconType: "workflow-icon",
		IconData: "workflow-data",
		Viewport: json.RawMessage(`{"x": 0, "y": 0, "zoom": 1}`),
	}

	workflow, _ := domain.ReconstructWorkflow(
		id,
		time.Now(),
		time.Now(),
		name,
		"Test workflow description",
		domain.WorkflowStatusDraft,
		userId,
		meta,
		nodes,
		edges,
	)
	return workflow
}

// 辅助函数：创建测试工作流节点
func createTestWorkflowNode(id uuid.UUID, name, nodeType string) *domain.WorkflowNode {
	meta := domain.NodeMeta{
		IconType: "node-icon",
		IconData: "node-data",
		Position: json.RawMessage(`{"x": 100, "y": 100}`),
	}

	node, _ := domain.ReconstructWorkflowNode(
		id,
		time.Now(),
		time.Now(),
		name,
		"Test node description",
		nodeType,
		"1.0.0",
		"test-plugin",
		"1.0.0",
		[]*domain.NodeParam{},
		[]*domain.NodeParam{},
		[]*domain.NodePort{},
		[]*domain.NodePort{},
		map[string]interface{}{},
		meta,
	)
	return node
}

// 辅助函数：创建测试工作流边
func createTestWorkflowEdge(id, fromNodeId, toNodeId uuid.UUID) *domain.WorkflowEdge {
	edge, _ := domain.ReconstructWorkflowEdge(
		id,
		time.Now(),
		time.Now(),
		fromNodeId,
		toNodeId,
		"output",
		"input",
		"data",
	)
	return edge
}

func TestEntWorkflowStore_Save_WithTransaction(t *testing.T) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	// 准备测试数据
	workflowId := uuid.New()
	userId := uuid.New()
	nodeId := uuid.New()
	edgeId := uuid.New()

	nodes := []*domain.WorkflowNode{
		createTestWorkflowNode(nodeId, "Node 1", "processor"),
	}

	edges := []*domain.WorkflowEdge{
		createTestWorkflowEdge(edgeId, nodeId, nodeId),
	}

	workflow := createTestWorkflow(workflowId, userId, "Test Workflow", nodes, edges)

	// 在事务中执行保存
	err := client.WithTx(ctx, func(tx *ent.Tx) error {
		txCtx := ent.NewTxContext(ctx, tx)
		return store.Save(txCtx, workflow)
	})
	require.NoError(t, err)

	// 验证数据已保存
	savedWorkflow, err := client.Workflow.Get(ctx, workflowId)
	require.NoError(t, err)
	assert.Equal(t, "Test Workflow", savedWorkflow.Name)

	// 验证节点和边也已保存
	nodeCount, err := client.WorkflowNode.Query().Where(workflownode.WorkflowID(workflowId)).Count(ctx)
	require.NoError(t, err)
	assert.Equal(t, 1, nodeCount)

	edgeCount, err := client.WorkflowsEdge.Query().Where(workflowsedge.WorkflowID(workflowId)).Count(ctx)
	require.NoError(t, err)
	assert.Equal(t, 1, edgeCount)
}

func TestEntWorkflowStore_Save_TransactionRollback(t *testing.T) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	workflowId := uuid.New()
	userId := uuid.New()
	nodeId := uuid.New()

	nodes := []*domain.WorkflowNode{
		createTestWorkflowNode(nodeId, "Node 1", "processor"),
	}

	workflow := createTestWorkflow(workflowId, userId, "Test Workflow", nodes, []*domain.WorkflowEdge{})

	// 模拟事务中的错误（通过在事务中手动返回错误）
	err := client.WithTx(ctx, func(tx *ent.Tx) error {
		txCtx := ent.NewTxContext(ctx, tx)

		// 先保存工作流
		err := store.Save(txCtx, workflow)
		if err != nil {
			return err
		}

		// 模拟后续操作失败
		return assert.AnError
	})
	require.Error(t, err)

	// 验证由于事务回滚，工作流没有被保存
	_, err = client.Workflow.Get(ctx, workflowId)
	assert.Error(t, err) // 应该返回NotFound错误

	// 验证节点也没有被保存
	nodeCount, err := client.WorkflowNode.Query().Where(workflownode.WorkflowID(workflowId)).Count(ctx)
	require.NoError(t, err)
	assert.Equal(t, 0, nodeCount)
}

// 基准测试
func BenchmarkEntWorkflowStore_Save(b *testing.B) {
	client := enttest.Open(b, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		workflowId := uuid.New()
		userId := uuid.New()
		nodeId := uuid.New()
		edgeId := uuid.New()

		nodes := []*domain.WorkflowNode{
			createTestWorkflowNode(nodeId, "Node 1", "processor"),
		}

		edges := []*domain.WorkflowEdge{
			createTestWorkflowEdge(edgeId, nodeId, nodeId),
		}

		workflow := createTestWorkflow(workflowId, userId, "Test Workflow", nodes, edges)

		err := store.Save(ctx, workflow)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkEntWorkflowStore_Find(b *testing.B) {
	client := enttest.Open(b, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	// 准备测试数据
	workflowIds := make([]uuid.UUID, 100)
	for i := 0; i < 100; i++ {
		workflowId := uuid.New()
		userId := uuid.New()
		nodeId := uuid.New()
		edgeId := uuid.New()

		nodes := []*domain.WorkflowNode{
			createTestWorkflowNode(nodeId, "Node 1", "processor"),
		}

		edges := []*domain.WorkflowEdge{
			createTestWorkflowEdge(edgeId, nodeId, nodeId),
		}

		workflow := createTestWorkflow(workflowId, userId, "Test Workflow", nodes, edges)
		err := store.Save(ctx, workflow)
		if err != nil {
			b.Fatal(err)
		}
		workflowIds[i] = workflowId
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		workflowId := workflowIds[i%100]
		_, err := store.Find(ctx, workflowId)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// 表格驱动测试 - 测试各种边界情况
func TestEntWorkflowStore_Save_EdgeCases(t *testing.T) {
	client := enttest.Open(t, "sqlite3", "file:ent?mode=memory&cache=shared&_fk=1")
	defer client.Close()

	store := NewEntWorkflowStore(client)
	ctx := context.Background()

	tests := []struct {
		name        string
		setupFunc   func() *domain.Workflow
		expectError bool
		errorMsg    string
	}{
		{
			name: "空节点和边的工作流",
			setupFunc: func() *domain.Workflow {
				return createTestWorkflow(uuid.New(), uuid.New(), "Empty Workflow", []*domain.WorkflowNode{}, []*domain.WorkflowEdge{})
			},
			expectError: false,
		},
		{
			name: "只有节点没有边的工作流",
			setupFunc: func() *domain.Workflow {
				nodeId := uuid.New()
				nodes := []*domain.WorkflowNode{
					createTestWorkflowNode(nodeId, "Single Node", "processor"),
				}
				return createTestWorkflow(uuid.New(), uuid.New(), "Node Only Workflow", nodes, []*domain.WorkflowEdge{})
			},
			expectError: false,
		},
		{
			name: "大量节点和边的工作流",
			setupFunc: func() *domain.Workflow {
				var nodes []*domain.WorkflowNode
				var edges []*domain.WorkflowEdge

				// 创建10个节点
				nodeIds := make([]uuid.UUID, 10)
				for i := 0; i < 10; i++ {
					nodeId := uuid.New()
					nodeIds[i] = nodeId
					nodes = append(nodes, createTestWorkflowNode(nodeId, fmt.Sprintf("Node %d", i), "processor"))
				}

				// 创建连接相邻节点的边
				for i := 0; i < 9; i++ {
					edgeId := uuid.New()
					edges = append(edges, createTestWorkflowEdge(edgeId, nodeIds[i], nodeIds[i+1]))
				}

				return createTestWorkflow(uuid.New(), uuid.New(), "Large Workflow", nodes, edges)
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			workflow := tt.setupFunc()

			err := store.Save(ctx, workflow)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)

				// 验证保存成功
				savedWorkflow, err := store.Find(ctx, workflow.ID())
				assert.NoError(t, err)
				assert.NotNil(t, savedWorkflow)
				assert.Equal(t, workflow.Name(), savedWorkflow.Name())
			}
		})
	}
}
