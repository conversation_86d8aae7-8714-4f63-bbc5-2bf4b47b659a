// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/workflow"
	"resflow/ent/workflowsedge"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowsEdgeCreate is the builder for creating a WorkflowsEdge entity.
type WorkflowsEdgeCreate struct {
	config
	mutation *WorkflowsEdgeMutation
	hooks    []Hook
}

// SetWorkflowID sets the "workflow_id" field.
func (wec *WorkflowsEdgeCreate) SetWorkflowID(u uuid.UUID) *WorkflowsEdgeCreate {
	wec.mutation.SetWorkflowID(u)
	return wec
}

// SetFromNodeID sets the "from_node_id" field.
func (wec *WorkflowsEdgeCreate) SetFromNodeID(u uuid.UUID) *WorkflowsEdgeCreate {
	wec.mutation.SetFromNodeID(u)
	return wec
}

// SetToNodeID sets the "to_node_id" field.
func (wec *WorkflowsEdgeCreate) SetToNodeID(u uuid.UUID) *WorkflowsEdgeCreate {
	wec.mutation.SetToNodeID(u)
	return wec
}

// SetFromPortID sets the "from_port_id" field.
func (wec *WorkflowsEdgeCreate) SetFromPortID(s string) *WorkflowsEdgeCreate {
	wec.mutation.SetFromPortID(s)
	return wec
}

// SetToPortID sets the "to_port_id" field.
func (wec *WorkflowsEdgeCreate) SetToPortID(s string) *WorkflowsEdgeCreate {
	wec.mutation.SetToPortID(s)
	return wec
}

// SetType sets the "type" field.
func (wec *WorkflowsEdgeCreate) SetType(s string) *WorkflowsEdgeCreate {
	wec.mutation.SetType(s)
	return wec
}

// SetCreatedAt sets the "created_at" field.
func (wec *WorkflowsEdgeCreate) SetCreatedAt(t time.Time) *WorkflowsEdgeCreate {
	wec.mutation.SetCreatedAt(t)
	return wec
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wec *WorkflowsEdgeCreate) SetNillableCreatedAt(t *time.Time) *WorkflowsEdgeCreate {
	if t != nil {
		wec.SetCreatedAt(*t)
	}
	return wec
}

// SetUpdatedAt sets the "updated_at" field.
func (wec *WorkflowsEdgeCreate) SetUpdatedAt(t time.Time) *WorkflowsEdgeCreate {
	wec.mutation.SetUpdatedAt(t)
	return wec
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wec *WorkflowsEdgeCreate) SetNillableUpdatedAt(t *time.Time) *WorkflowsEdgeCreate {
	if t != nil {
		wec.SetUpdatedAt(*t)
	}
	return wec
}

// SetID sets the "id" field.
func (wec *WorkflowsEdgeCreate) SetID(u uuid.UUID) *WorkflowsEdgeCreate {
	wec.mutation.SetID(u)
	return wec
}

// SetNillableID sets the "id" field if the given value is not nil.
func (wec *WorkflowsEdgeCreate) SetNillableID(u *uuid.UUID) *WorkflowsEdgeCreate {
	if u != nil {
		wec.SetID(*u)
	}
	return wec
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (wec *WorkflowsEdgeCreate) SetWorkflow(w *Workflow) *WorkflowsEdgeCreate {
	return wec.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowsEdgeMutation object of the builder.
func (wec *WorkflowsEdgeCreate) Mutation() *WorkflowsEdgeMutation {
	return wec.mutation
}

// Save creates the WorkflowsEdge in the database.
func (wec *WorkflowsEdgeCreate) Save(ctx context.Context) (*WorkflowsEdge, error) {
	wec.defaults()
	return withHooks(ctx, wec.sqlSave, wec.mutation, wec.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wec *WorkflowsEdgeCreate) SaveX(ctx context.Context) *WorkflowsEdge {
	v, err := wec.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wec *WorkflowsEdgeCreate) Exec(ctx context.Context) error {
	_, err := wec.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wec *WorkflowsEdgeCreate) ExecX(ctx context.Context) {
	if err := wec.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wec *WorkflowsEdgeCreate) defaults() {
	if _, ok := wec.mutation.CreatedAt(); !ok {
		v := workflowsedge.DefaultCreatedAt()
		wec.mutation.SetCreatedAt(v)
	}
	if _, ok := wec.mutation.UpdatedAt(); !ok {
		v := workflowsedge.DefaultUpdatedAt()
		wec.mutation.SetUpdatedAt(v)
	}
	if _, ok := wec.mutation.ID(); !ok {
		v := workflowsedge.DefaultID()
		wec.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (wec *WorkflowsEdgeCreate) check() error {
	if _, ok := wec.mutation.WorkflowID(); !ok {
		return &ValidationError{Name: "workflow_id", err: errors.New(`ent: missing required field "WorkflowsEdge.workflow_id"`)}
	}
	if _, ok := wec.mutation.FromNodeID(); !ok {
		return &ValidationError{Name: "from_node_id", err: errors.New(`ent: missing required field "WorkflowsEdge.from_node_id"`)}
	}
	if _, ok := wec.mutation.ToNodeID(); !ok {
		return &ValidationError{Name: "to_node_id", err: errors.New(`ent: missing required field "WorkflowsEdge.to_node_id"`)}
	}
	if _, ok := wec.mutation.FromPortID(); !ok {
		return &ValidationError{Name: "from_port_id", err: errors.New(`ent: missing required field "WorkflowsEdge.from_port_id"`)}
	}
	if _, ok := wec.mutation.ToPortID(); !ok {
		return &ValidationError{Name: "to_port_id", err: errors.New(`ent: missing required field "WorkflowsEdge.to_port_id"`)}
	}
	if _, ok := wec.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "WorkflowsEdge.type"`)}
	}
	if _, ok := wec.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "WorkflowsEdge.created_at"`)}
	}
	if _, ok := wec.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "WorkflowsEdge.updated_at"`)}
	}
	if len(wec.mutation.WorkflowIDs()) == 0 {
		return &ValidationError{Name: "workflow", err: errors.New(`ent: missing required edge "WorkflowsEdge.workflow"`)}
	}
	return nil
}

func (wec *WorkflowsEdgeCreate) sqlSave(ctx context.Context) (*WorkflowsEdge, error) {
	if err := wec.check(); err != nil {
		return nil, err
	}
	_node, _spec := wec.createSpec()
	if err := sqlgraph.CreateNode(ctx, wec.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	wec.mutation.id = &_node.ID
	wec.mutation.done = true
	return _node, nil
}

func (wec *WorkflowsEdgeCreate) createSpec() (*WorkflowsEdge, *sqlgraph.CreateSpec) {
	var (
		_node = &WorkflowsEdge{config: wec.config}
		_spec = sqlgraph.NewCreateSpec(workflowsedge.Table, sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID))
	)
	if id, ok := wec.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := wec.mutation.FromNodeID(); ok {
		_spec.SetField(workflowsedge.FieldFromNodeID, field.TypeUUID, value)
		_node.FromNodeID = value
	}
	if value, ok := wec.mutation.ToNodeID(); ok {
		_spec.SetField(workflowsedge.FieldToNodeID, field.TypeUUID, value)
		_node.ToNodeID = value
	}
	if value, ok := wec.mutation.FromPortID(); ok {
		_spec.SetField(workflowsedge.FieldFromPortID, field.TypeString, value)
		_node.FromPortID = value
	}
	if value, ok := wec.mutation.ToPortID(); ok {
		_spec.SetField(workflowsedge.FieldToPortID, field.TypeString, value)
		_node.ToPortID = value
	}
	if value, ok := wec.mutation.GetType(); ok {
		_spec.SetField(workflowsedge.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := wec.mutation.CreatedAt(); ok {
		_spec.SetField(workflowsedge.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wec.mutation.UpdatedAt(); ok {
		_spec.SetField(workflowsedge.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if nodes := wec.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowsedge.WorkflowTable,
			Columns: []string{workflowsedge.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.WorkflowID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// WorkflowsEdgeCreateBulk is the builder for creating many WorkflowsEdge entities in bulk.
type WorkflowsEdgeCreateBulk struct {
	config
	err      error
	builders []*WorkflowsEdgeCreate
}

// Save creates the WorkflowsEdge entities in the database.
func (wecb *WorkflowsEdgeCreateBulk) Save(ctx context.Context) ([]*WorkflowsEdge, error) {
	if wecb.err != nil {
		return nil, wecb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wecb.builders))
	nodes := make([]*WorkflowsEdge, len(wecb.builders))
	mutators := make([]Mutator, len(wecb.builders))
	for i := range wecb.builders {
		func(i int, root context.Context) {
			builder := wecb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WorkflowsEdgeMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wecb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wecb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wecb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wecb *WorkflowsEdgeCreateBulk) SaveX(ctx context.Context) []*WorkflowsEdge {
	v, err := wecb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wecb *WorkflowsEdgeCreateBulk) Exec(ctx context.Context) error {
	_, err := wecb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wecb *WorkflowsEdgeCreateBulk) ExecX(ctx context.Context) {
	if err := wecb.Exec(ctx); err != nil {
		panic(err)
	}
}
