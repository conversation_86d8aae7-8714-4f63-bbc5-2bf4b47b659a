package schema

import (
	"encoding/json"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowNode holds the schema definition for the WorkflowNode entity.
type WorkflowNode struct {
	ent.Schema
}

// Fields of the WorkflowNode.
func (WorkflowNode) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}),
		field.UUID("workflow_id", uuid.UUID{}),
		field.String("name"),
		field.String("description"),
		field.String("icon_type"),
		field.String("icon_data"),
		field.String("type"),
		field.String("version"),
		field.String("plugin_name"),
		field.String("plugin_version"),
		field.JSON("input_params", json.RawMessage{}),
		field.JSON("output_params", json.RawMessage{}),
		field.JSON("input_ports", json.RawMessage{}),
		field.JSON("output_ports", json.RawMessage{}),
		field.JSON("position", json.RawMessage{}),
		field.JSON("config_data", json.RawMessage{}).Optional(),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the WorkflowNode.
func (WorkflowNode) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("workflow", Workflow.Type).Ref("nodes").Unique().Field("workflow_id").Required(),
	}
}
