package schema

import (
	"encoding/json"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// Workflow holds the schema definition for the Workflow entity.
type Workflow struct {
	ent.Schema
}

// Fields of the Workflow.
func (Workflow) Fields() []ent.Field {
	return []ent.Field{
		field.UUID("id", uuid.UUID{}),
		field.UUID("user_id", uuid.UUID{}).Optional(),
		field.String("name"),
		field.String("icon_type"),
		field.String("icon_data"),
		field.String("description"),
		field.Int("status"),
		field.JSON("viewport", json.RawMessage{}),
		field.Time("created_at").Default(time.Now).Immutable(),
		field.Time("updated_at").Default(time.Now),
	}
}

// Edges of the Workflow.
func (Workflow) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("user", User.Type).Ref("workflows").Unique().Field("user_id"),
		edge.To("nodes", WorkflowNode.Type),
		edge.To("workflow_edges", WorkflowsEdge.Type),
	}
}
