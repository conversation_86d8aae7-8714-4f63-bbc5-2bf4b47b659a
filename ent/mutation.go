// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"resflow/ent/nodedefinition"
	"resflow/ent/plugin"
	"resflow/ent/predicate"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"
	"resflow/internal/plugin/domain"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeNodeDefinition = "NodeDefinition"
	TypePlugin         = "Plugin"
	TypeUser           = "User"
	TypeWorkflow       = "Workflow"
	TypeWorkflowNode   = "WorkflowNode"
	TypeWorkflowsEdge  = "WorkflowsEdge"
)

// NodeDefinitionMutation represents an operation that mutates the NodeDefinition nodes in the graph.
type NodeDefinitionMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	plugin_name         *string
	name                *string
	author              *string
	description         *string
	icon                *string
	_type               *string
	version             *string
	category            *string
	input_params        *[]*domain.NodeParam
	appendinput_params  []*domain.NodeParam
	output_params       *[]*domain.NodeParam
	appendoutput_params []*domain.NodeParam
	input_ports         *[]*domain.NodePort
	appendinput_ports   []*domain.NodePort
	output_ports        *[]*domain.NodePort
	appendoutput_ports  []*domain.NodePort
	exception           *bool
	_path               *string
	builtin             *bool
	enabled             *bool
	created_at          *time.Time
	updated_at          *time.Time
	clearedFields       map[string]struct{}
	done                bool
	oldValue            func(context.Context) (*NodeDefinition, error)
	predicates          []predicate.NodeDefinition
}

var _ ent.Mutation = (*NodeDefinitionMutation)(nil)

// nodedefinitionOption allows management of the mutation configuration using functional options.
type nodedefinitionOption func(*NodeDefinitionMutation)

// newNodeDefinitionMutation creates new mutation for the NodeDefinition entity.
func newNodeDefinitionMutation(c config, op Op, opts ...nodedefinitionOption) *NodeDefinitionMutation {
	m := &NodeDefinitionMutation{
		config:        c,
		op:            op,
		typ:           TypeNodeDefinition,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withNodeDefinitionID sets the ID field of the mutation.
func withNodeDefinitionID(id uuid.UUID) nodedefinitionOption {
	return func(m *NodeDefinitionMutation) {
		var (
			err   error
			once  sync.Once
			value *NodeDefinition
		)
		m.oldValue = func(ctx context.Context) (*NodeDefinition, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().NodeDefinition.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withNodeDefinition sets the old NodeDefinition of the mutation.
func withNodeDefinition(node *NodeDefinition) nodedefinitionOption {
	return func(m *NodeDefinitionMutation) {
		m.oldValue = func(context.Context) (*NodeDefinition, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m NodeDefinitionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m NodeDefinitionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of NodeDefinition entities.
func (m *NodeDefinitionMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *NodeDefinitionMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *NodeDefinitionMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().NodeDefinition.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetPluginName sets the "plugin_name" field.
func (m *NodeDefinitionMutation) SetPluginName(s string) {
	m.plugin_name = &s
}

// PluginName returns the value of the "plugin_name" field in the mutation.
func (m *NodeDefinitionMutation) PluginName() (r string, exists bool) {
	v := m.plugin_name
	if v == nil {
		return
	}
	return *v, true
}

// OldPluginName returns the old "plugin_name" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldPluginName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPluginName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPluginName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPluginName: %w", err)
	}
	return oldValue.PluginName, nil
}

// ResetPluginName resets all changes to the "plugin_name" field.
func (m *NodeDefinitionMutation) ResetPluginName() {
	m.plugin_name = nil
}

// SetName sets the "name" field.
func (m *NodeDefinitionMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *NodeDefinitionMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *NodeDefinitionMutation) ResetName() {
	m.name = nil
}

// SetAuthor sets the "author" field.
func (m *NodeDefinitionMutation) SetAuthor(s string) {
	m.author = &s
}

// Author returns the value of the "author" field in the mutation.
func (m *NodeDefinitionMutation) Author() (r string, exists bool) {
	v := m.author
	if v == nil {
		return
	}
	return *v, true
}

// OldAuthor returns the old "author" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldAuthor(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAuthor is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAuthor requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAuthor: %w", err)
	}
	return oldValue.Author, nil
}

// ResetAuthor resets all changes to the "author" field.
func (m *NodeDefinitionMutation) ResetAuthor() {
	m.author = nil
}

// SetDescription sets the "description" field.
func (m *NodeDefinitionMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *NodeDefinitionMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *NodeDefinitionMutation) ResetDescription() {
	m.description = nil
}

// SetIcon sets the "icon" field.
func (m *NodeDefinitionMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *NodeDefinitionMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ResetIcon resets all changes to the "icon" field.
func (m *NodeDefinitionMutation) ResetIcon() {
	m.icon = nil
}

// SetType sets the "type" field.
func (m *NodeDefinitionMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *NodeDefinitionMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *NodeDefinitionMutation) ResetType() {
	m._type = nil
}

// SetVersion sets the "version" field.
func (m *NodeDefinitionMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *NodeDefinitionMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *NodeDefinitionMutation) ResetVersion() {
	m.version = nil
}

// SetCategory sets the "category" field.
func (m *NodeDefinitionMutation) SetCategory(s string) {
	m.category = &s
}

// Category returns the value of the "category" field in the mutation.
func (m *NodeDefinitionMutation) Category() (r string, exists bool) {
	v := m.category
	if v == nil {
		return
	}
	return *v, true
}

// OldCategory returns the old "category" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldCategory(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCategory is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCategory requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCategory: %w", err)
	}
	return oldValue.Category, nil
}

// ResetCategory resets all changes to the "category" field.
func (m *NodeDefinitionMutation) ResetCategory() {
	m.category = nil
}

// SetInputParams sets the "input_params" field.
func (m *NodeDefinitionMutation) SetInputParams(dp []*domain.NodeParam) {
	m.input_params = &dp
	m.appendinput_params = nil
}

// InputParams returns the value of the "input_params" field in the mutation.
func (m *NodeDefinitionMutation) InputParams() (r []*domain.NodeParam, exists bool) {
	v := m.input_params
	if v == nil {
		return
	}
	return *v, true
}

// OldInputParams returns the old "input_params" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldInputParams(ctx context.Context) (v []*domain.NodeParam, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputParams: %w", err)
	}
	return oldValue.InputParams, nil
}

// AppendInputParams adds dp to the "input_params" field.
func (m *NodeDefinitionMutation) AppendInputParams(dp []*domain.NodeParam) {
	m.appendinput_params = append(m.appendinput_params, dp...)
}

// AppendedInputParams returns the list of values that were appended to the "input_params" field in this mutation.
func (m *NodeDefinitionMutation) AppendedInputParams() ([]*domain.NodeParam, bool) {
	if len(m.appendinput_params) == 0 {
		return nil, false
	}
	return m.appendinput_params, true
}

// ResetInputParams resets all changes to the "input_params" field.
func (m *NodeDefinitionMutation) ResetInputParams() {
	m.input_params = nil
	m.appendinput_params = nil
}

// SetOutputParams sets the "output_params" field.
func (m *NodeDefinitionMutation) SetOutputParams(dp []*domain.NodeParam) {
	m.output_params = &dp
	m.appendoutput_params = nil
}

// OutputParams returns the value of the "output_params" field in the mutation.
func (m *NodeDefinitionMutation) OutputParams() (r []*domain.NodeParam, exists bool) {
	v := m.output_params
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputParams returns the old "output_params" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldOutputParams(ctx context.Context) (v []*domain.NodeParam, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputParams: %w", err)
	}
	return oldValue.OutputParams, nil
}

// AppendOutputParams adds dp to the "output_params" field.
func (m *NodeDefinitionMutation) AppendOutputParams(dp []*domain.NodeParam) {
	m.appendoutput_params = append(m.appendoutput_params, dp...)
}

// AppendedOutputParams returns the list of values that were appended to the "output_params" field in this mutation.
func (m *NodeDefinitionMutation) AppendedOutputParams() ([]*domain.NodeParam, bool) {
	if len(m.appendoutput_params) == 0 {
		return nil, false
	}
	return m.appendoutput_params, true
}

// ResetOutputParams resets all changes to the "output_params" field.
func (m *NodeDefinitionMutation) ResetOutputParams() {
	m.output_params = nil
	m.appendoutput_params = nil
}

// SetInputPorts sets the "input_ports" field.
func (m *NodeDefinitionMutation) SetInputPorts(dp []*domain.NodePort) {
	m.input_ports = &dp
	m.appendinput_ports = nil
}

// InputPorts returns the value of the "input_ports" field in the mutation.
func (m *NodeDefinitionMutation) InputPorts() (r []*domain.NodePort, exists bool) {
	v := m.input_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldInputPorts returns the old "input_ports" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldInputPorts(ctx context.Context) (v []*domain.NodePort, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputPorts: %w", err)
	}
	return oldValue.InputPorts, nil
}

// AppendInputPorts adds dp to the "input_ports" field.
func (m *NodeDefinitionMutation) AppendInputPorts(dp []*domain.NodePort) {
	m.appendinput_ports = append(m.appendinput_ports, dp...)
}

// AppendedInputPorts returns the list of values that were appended to the "input_ports" field in this mutation.
func (m *NodeDefinitionMutation) AppendedInputPorts() ([]*domain.NodePort, bool) {
	if len(m.appendinput_ports) == 0 {
		return nil, false
	}
	return m.appendinput_ports, true
}

// ResetInputPorts resets all changes to the "input_ports" field.
func (m *NodeDefinitionMutation) ResetInputPorts() {
	m.input_ports = nil
	m.appendinput_ports = nil
}

// SetOutputPorts sets the "output_ports" field.
func (m *NodeDefinitionMutation) SetOutputPorts(dp []*domain.NodePort) {
	m.output_ports = &dp
	m.appendoutput_ports = nil
}

// OutputPorts returns the value of the "output_ports" field in the mutation.
func (m *NodeDefinitionMutation) OutputPorts() (r []*domain.NodePort, exists bool) {
	v := m.output_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputPorts returns the old "output_ports" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldOutputPorts(ctx context.Context) (v []*domain.NodePort, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputPorts: %w", err)
	}
	return oldValue.OutputPorts, nil
}

// AppendOutputPorts adds dp to the "output_ports" field.
func (m *NodeDefinitionMutation) AppendOutputPorts(dp []*domain.NodePort) {
	m.appendoutput_ports = append(m.appendoutput_ports, dp...)
}

// AppendedOutputPorts returns the list of values that were appended to the "output_ports" field in this mutation.
func (m *NodeDefinitionMutation) AppendedOutputPorts() ([]*domain.NodePort, bool) {
	if len(m.appendoutput_ports) == 0 {
		return nil, false
	}
	return m.appendoutput_ports, true
}

// ResetOutputPorts resets all changes to the "output_ports" field.
func (m *NodeDefinitionMutation) ResetOutputPorts() {
	m.output_ports = nil
	m.appendoutput_ports = nil
}

// SetException sets the "exception" field.
func (m *NodeDefinitionMutation) SetException(b bool) {
	m.exception = &b
}

// Exception returns the value of the "exception" field in the mutation.
func (m *NodeDefinitionMutation) Exception() (r bool, exists bool) {
	v := m.exception
	if v == nil {
		return
	}
	return *v, true
}

// OldException returns the old "exception" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldException(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldException is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldException requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldException: %w", err)
	}
	return oldValue.Exception, nil
}

// ResetException resets all changes to the "exception" field.
func (m *NodeDefinitionMutation) ResetException() {
	m.exception = nil
}

// SetPath sets the "path" field.
func (m *NodeDefinitionMutation) SetPath(s string) {
	m._path = &s
}

// Path returns the value of the "path" field in the mutation.
func (m *NodeDefinitionMutation) Path() (r string, exists bool) {
	v := m._path
	if v == nil {
		return
	}
	return *v, true
}

// OldPath returns the old "path" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldPath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPath: %w", err)
	}
	return oldValue.Path, nil
}

// ResetPath resets all changes to the "path" field.
func (m *NodeDefinitionMutation) ResetPath() {
	m._path = nil
}

// SetBuiltin sets the "builtin" field.
func (m *NodeDefinitionMutation) SetBuiltin(b bool) {
	m.builtin = &b
}

// Builtin returns the value of the "builtin" field in the mutation.
func (m *NodeDefinitionMutation) Builtin() (r bool, exists bool) {
	v := m.builtin
	if v == nil {
		return
	}
	return *v, true
}

// OldBuiltin returns the old "builtin" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldBuiltin(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBuiltin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBuiltin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBuiltin: %w", err)
	}
	return oldValue.Builtin, nil
}

// ResetBuiltin resets all changes to the "builtin" field.
func (m *NodeDefinitionMutation) ResetBuiltin() {
	m.builtin = nil
}

// SetEnabled sets the "enabled" field.
func (m *NodeDefinitionMutation) SetEnabled(b bool) {
	m.enabled = &b
}

// Enabled returns the value of the "enabled" field in the mutation.
func (m *NodeDefinitionMutation) Enabled() (r bool, exists bool) {
	v := m.enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldEnabled returns the old "enabled" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnabled: %w", err)
	}
	return oldValue.Enabled, nil
}

// ResetEnabled resets all changes to the "enabled" field.
func (m *NodeDefinitionMutation) ResetEnabled() {
	m.enabled = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *NodeDefinitionMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *NodeDefinitionMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *NodeDefinitionMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *NodeDefinitionMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *NodeDefinitionMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *NodeDefinitionMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// Where appends a list predicates to the NodeDefinitionMutation builder.
func (m *NodeDefinitionMutation) Where(ps ...predicate.NodeDefinition) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the NodeDefinitionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *NodeDefinitionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.NodeDefinition, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *NodeDefinitionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *NodeDefinitionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (NodeDefinition).
func (m *NodeDefinitionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *NodeDefinitionMutation) Fields() []string {
	fields := make([]string, 0, 18)
	if m.plugin_name != nil {
		fields = append(fields, nodedefinition.FieldPluginName)
	}
	if m.name != nil {
		fields = append(fields, nodedefinition.FieldName)
	}
	if m.author != nil {
		fields = append(fields, nodedefinition.FieldAuthor)
	}
	if m.description != nil {
		fields = append(fields, nodedefinition.FieldDescription)
	}
	if m.icon != nil {
		fields = append(fields, nodedefinition.FieldIcon)
	}
	if m._type != nil {
		fields = append(fields, nodedefinition.FieldType)
	}
	if m.version != nil {
		fields = append(fields, nodedefinition.FieldVersion)
	}
	if m.category != nil {
		fields = append(fields, nodedefinition.FieldCategory)
	}
	if m.input_params != nil {
		fields = append(fields, nodedefinition.FieldInputParams)
	}
	if m.output_params != nil {
		fields = append(fields, nodedefinition.FieldOutputParams)
	}
	if m.input_ports != nil {
		fields = append(fields, nodedefinition.FieldInputPorts)
	}
	if m.output_ports != nil {
		fields = append(fields, nodedefinition.FieldOutputPorts)
	}
	if m.exception != nil {
		fields = append(fields, nodedefinition.FieldException)
	}
	if m._path != nil {
		fields = append(fields, nodedefinition.FieldPath)
	}
	if m.builtin != nil {
		fields = append(fields, nodedefinition.FieldBuiltin)
	}
	if m.enabled != nil {
		fields = append(fields, nodedefinition.FieldEnabled)
	}
	if m.created_at != nil {
		fields = append(fields, nodedefinition.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, nodedefinition.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *NodeDefinitionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case nodedefinition.FieldPluginName:
		return m.PluginName()
	case nodedefinition.FieldName:
		return m.Name()
	case nodedefinition.FieldAuthor:
		return m.Author()
	case nodedefinition.FieldDescription:
		return m.Description()
	case nodedefinition.FieldIcon:
		return m.Icon()
	case nodedefinition.FieldType:
		return m.GetType()
	case nodedefinition.FieldVersion:
		return m.Version()
	case nodedefinition.FieldCategory:
		return m.Category()
	case nodedefinition.FieldInputParams:
		return m.InputParams()
	case nodedefinition.FieldOutputParams:
		return m.OutputParams()
	case nodedefinition.FieldInputPorts:
		return m.InputPorts()
	case nodedefinition.FieldOutputPorts:
		return m.OutputPorts()
	case nodedefinition.FieldException:
		return m.Exception()
	case nodedefinition.FieldPath:
		return m.Path()
	case nodedefinition.FieldBuiltin:
		return m.Builtin()
	case nodedefinition.FieldEnabled:
		return m.Enabled()
	case nodedefinition.FieldCreatedAt:
		return m.CreatedAt()
	case nodedefinition.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *NodeDefinitionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case nodedefinition.FieldPluginName:
		return m.OldPluginName(ctx)
	case nodedefinition.FieldName:
		return m.OldName(ctx)
	case nodedefinition.FieldAuthor:
		return m.OldAuthor(ctx)
	case nodedefinition.FieldDescription:
		return m.OldDescription(ctx)
	case nodedefinition.FieldIcon:
		return m.OldIcon(ctx)
	case nodedefinition.FieldType:
		return m.OldType(ctx)
	case nodedefinition.FieldVersion:
		return m.OldVersion(ctx)
	case nodedefinition.FieldCategory:
		return m.OldCategory(ctx)
	case nodedefinition.FieldInputParams:
		return m.OldInputParams(ctx)
	case nodedefinition.FieldOutputParams:
		return m.OldOutputParams(ctx)
	case nodedefinition.FieldInputPorts:
		return m.OldInputPorts(ctx)
	case nodedefinition.FieldOutputPorts:
		return m.OldOutputPorts(ctx)
	case nodedefinition.FieldException:
		return m.OldException(ctx)
	case nodedefinition.FieldPath:
		return m.OldPath(ctx)
	case nodedefinition.FieldBuiltin:
		return m.OldBuiltin(ctx)
	case nodedefinition.FieldEnabled:
		return m.OldEnabled(ctx)
	case nodedefinition.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case nodedefinition.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown NodeDefinition field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NodeDefinitionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case nodedefinition.FieldPluginName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPluginName(v)
		return nil
	case nodedefinition.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case nodedefinition.FieldAuthor:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAuthor(v)
		return nil
	case nodedefinition.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case nodedefinition.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case nodedefinition.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case nodedefinition.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case nodedefinition.FieldCategory:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCategory(v)
		return nil
	case nodedefinition.FieldInputParams:
		v, ok := value.([]*domain.NodeParam)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputParams(v)
		return nil
	case nodedefinition.FieldOutputParams:
		v, ok := value.([]*domain.NodeParam)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputParams(v)
		return nil
	case nodedefinition.FieldInputPorts:
		v, ok := value.([]*domain.NodePort)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputPorts(v)
		return nil
	case nodedefinition.FieldOutputPorts:
		v, ok := value.([]*domain.NodePort)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputPorts(v)
		return nil
	case nodedefinition.FieldException:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetException(v)
		return nil
	case nodedefinition.FieldPath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPath(v)
		return nil
	case nodedefinition.FieldBuiltin:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBuiltin(v)
		return nil
	case nodedefinition.FieldEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnabled(v)
		return nil
	case nodedefinition.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case nodedefinition.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown NodeDefinition field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *NodeDefinitionMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *NodeDefinitionMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NodeDefinitionMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown NodeDefinition numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *NodeDefinitionMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *NodeDefinitionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *NodeDefinitionMutation) ClearField(name string) error {
	return fmt.Errorf("unknown NodeDefinition nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *NodeDefinitionMutation) ResetField(name string) error {
	switch name {
	case nodedefinition.FieldPluginName:
		m.ResetPluginName()
		return nil
	case nodedefinition.FieldName:
		m.ResetName()
		return nil
	case nodedefinition.FieldAuthor:
		m.ResetAuthor()
		return nil
	case nodedefinition.FieldDescription:
		m.ResetDescription()
		return nil
	case nodedefinition.FieldIcon:
		m.ResetIcon()
		return nil
	case nodedefinition.FieldType:
		m.ResetType()
		return nil
	case nodedefinition.FieldVersion:
		m.ResetVersion()
		return nil
	case nodedefinition.FieldCategory:
		m.ResetCategory()
		return nil
	case nodedefinition.FieldInputParams:
		m.ResetInputParams()
		return nil
	case nodedefinition.FieldOutputParams:
		m.ResetOutputParams()
		return nil
	case nodedefinition.FieldInputPorts:
		m.ResetInputPorts()
		return nil
	case nodedefinition.FieldOutputPorts:
		m.ResetOutputPorts()
		return nil
	case nodedefinition.FieldException:
		m.ResetException()
		return nil
	case nodedefinition.FieldPath:
		m.ResetPath()
		return nil
	case nodedefinition.FieldBuiltin:
		m.ResetBuiltin()
		return nil
	case nodedefinition.FieldEnabled:
		m.ResetEnabled()
		return nil
	case nodedefinition.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case nodedefinition.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown NodeDefinition field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *NodeDefinitionMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *NodeDefinitionMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *NodeDefinitionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *NodeDefinitionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *NodeDefinitionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *NodeDefinitionMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *NodeDefinitionMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown NodeDefinition unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *NodeDefinitionMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown NodeDefinition edge %s", name)
}

// PluginMutation represents an operation that mutates the Plugin nodes in the graph.
type PluginMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	name          *string
	version       *string
	author        *string
	display_name  *string
	description   *string
	icon          *string
	_path         *string
	builtin       *bool
	enabled       *bool
	created_at    *time.Time
	updated_at    *time.Time
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Plugin, error)
	predicates    []predicate.Plugin
}

var _ ent.Mutation = (*PluginMutation)(nil)

// pluginOption allows management of the mutation configuration using functional options.
type pluginOption func(*PluginMutation)

// newPluginMutation creates new mutation for the Plugin entity.
func newPluginMutation(c config, op Op, opts ...pluginOption) *PluginMutation {
	m := &PluginMutation{
		config:        c,
		op:            op,
		typ:           TypePlugin,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPluginID sets the ID field of the mutation.
func withPluginID(id uuid.UUID) pluginOption {
	return func(m *PluginMutation) {
		var (
			err   error
			once  sync.Once
			value *Plugin
		)
		m.oldValue = func(ctx context.Context) (*Plugin, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Plugin.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPlugin sets the old Plugin of the mutation.
func withPlugin(node *Plugin) pluginOption {
	return func(m *PluginMutation) {
		m.oldValue = func(context.Context) (*Plugin, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PluginMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PluginMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Plugin entities.
func (m *PluginMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PluginMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PluginMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Plugin.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *PluginMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *PluginMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *PluginMutation) ResetName() {
	m.name = nil
}

// SetVersion sets the "version" field.
func (m *PluginMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *PluginMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *PluginMutation) ResetVersion() {
	m.version = nil
}

// SetAuthor sets the "author" field.
func (m *PluginMutation) SetAuthor(s string) {
	m.author = &s
}

// Author returns the value of the "author" field in the mutation.
func (m *PluginMutation) Author() (r string, exists bool) {
	v := m.author
	if v == nil {
		return
	}
	return *v, true
}

// OldAuthor returns the old "author" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldAuthor(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAuthor is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAuthor requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAuthor: %w", err)
	}
	return oldValue.Author, nil
}

// ResetAuthor resets all changes to the "author" field.
func (m *PluginMutation) ResetAuthor() {
	m.author = nil
}

// SetDisplayName sets the "display_name" field.
func (m *PluginMutation) SetDisplayName(s string) {
	m.display_name = &s
}

// DisplayName returns the value of the "display_name" field in the mutation.
func (m *PluginMutation) DisplayName() (r string, exists bool) {
	v := m.display_name
	if v == nil {
		return
	}
	return *v, true
}

// OldDisplayName returns the old "display_name" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldDisplayName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDisplayName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDisplayName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDisplayName: %w", err)
	}
	return oldValue.DisplayName, nil
}

// ResetDisplayName resets all changes to the "display_name" field.
func (m *PluginMutation) ResetDisplayName() {
	m.display_name = nil
}

// SetDescription sets the "description" field.
func (m *PluginMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *PluginMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *PluginMutation) ResetDescription() {
	m.description = nil
}

// SetIcon sets the "icon" field.
func (m *PluginMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *PluginMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ResetIcon resets all changes to the "icon" field.
func (m *PluginMutation) ResetIcon() {
	m.icon = nil
}

// SetPath sets the "path" field.
func (m *PluginMutation) SetPath(s string) {
	m._path = &s
}

// Path returns the value of the "path" field in the mutation.
func (m *PluginMutation) Path() (r string, exists bool) {
	v := m._path
	if v == nil {
		return
	}
	return *v, true
}

// OldPath returns the old "path" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldPath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPath: %w", err)
	}
	return oldValue.Path, nil
}

// ResetPath resets all changes to the "path" field.
func (m *PluginMutation) ResetPath() {
	m._path = nil
}

// SetBuiltin sets the "builtin" field.
func (m *PluginMutation) SetBuiltin(b bool) {
	m.builtin = &b
}

// Builtin returns the value of the "builtin" field in the mutation.
func (m *PluginMutation) Builtin() (r bool, exists bool) {
	v := m.builtin
	if v == nil {
		return
	}
	return *v, true
}

// OldBuiltin returns the old "builtin" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldBuiltin(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBuiltin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBuiltin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBuiltin: %w", err)
	}
	return oldValue.Builtin, nil
}

// ResetBuiltin resets all changes to the "builtin" field.
func (m *PluginMutation) ResetBuiltin() {
	m.builtin = nil
}

// SetEnabled sets the "enabled" field.
func (m *PluginMutation) SetEnabled(b bool) {
	m.enabled = &b
}

// Enabled returns the value of the "enabled" field in the mutation.
func (m *PluginMutation) Enabled() (r bool, exists bool) {
	v := m.enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldEnabled returns the old "enabled" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnabled: %w", err)
	}
	return oldValue.Enabled, nil
}

// ResetEnabled resets all changes to the "enabled" field.
func (m *PluginMutation) ResetEnabled() {
	m.enabled = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *PluginMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *PluginMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *PluginMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *PluginMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *PluginMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *PluginMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// Where appends a list predicates to the PluginMutation builder.
func (m *PluginMutation) Where(ps ...predicate.Plugin) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PluginMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PluginMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Plugin, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PluginMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PluginMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Plugin).
func (m *PluginMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PluginMutation) Fields() []string {
	fields := make([]string, 0, 11)
	if m.name != nil {
		fields = append(fields, plugin.FieldName)
	}
	if m.version != nil {
		fields = append(fields, plugin.FieldVersion)
	}
	if m.author != nil {
		fields = append(fields, plugin.FieldAuthor)
	}
	if m.display_name != nil {
		fields = append(fields, plugin.FieldDisplayName)
	}
	if m.description != nil {
		fields = append(fields, plugin.FieldDescription)
	}
	if m.icon != nil {
		fields = append(fields, plugin.FieldIcon)
	}
	if m._path != nil {
		fields = append(fields, plugin.FieldPath)
	}
	if m.builtin != nil {
		fields = append(fields, plugin.FieldBuiltin)
	}
	if m.enabled != nil {
		fields = append(fields, plugin.FieldEnabled)
	}
	if m.created_at != nil {
		fields = append(fields, plugin.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, plugin.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PluginMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case plugin.FieldName:
		return m.Name()
	case plugin.FieldVersion:
		return m.Version()
	case plugin.FieldAuthor:
		return m.Author()
	case plugin.FieldDisplayName:
		return m.DisplayName()
	case plugin.FieldDescription:
		return m.Description()
	case plugin.FieldIcon:
		return m.Icon()
	case plugin.FieldPath:
		return m.Path()
	case plugin.FieldBuiltin:
		return m.Builtin()
	case plugin.FieldEnabled:
		return m.Enabled()
	case plugin.FieldCreatedAt:
		return m.CreatedAt()
	case plugin.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PluginMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case plugin.FieldName:
		return m.OldName(ctx)
	case plugin.FieldVersion:
		return m.OldVersion(ctx)
	case plugin.FieldAuthor:
		return m.OldAuthor(ctx)
	case plugin.FieldDisplayName:
		return m.OldDisplayName(ctx)
	case plugin.FieldDescription:
		return m.OldDescription(ctx)
	case plugin.FieldIcon:
		return m.OldIcon(ctx)
	case plugin.FieldPath:
		return m.OldPath(ctx)
	case plugin.FieldBuiltin:
		return m.OldBuiltin(ctx)
	case plugin.FieldEnabled:
		return m.OldEnabled(ctx)
	case plugin.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case plugin.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Plugin field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PluginMutation) SetField(name string, value ent.Value) error {
	switch name {
	case plugin.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case plugin.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case plugin.FieldAuthor:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAuthor(v)
		return nil
	case plugin.FieldDisplayName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDisplayName(v)
		return nil
	case plugin.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case plugin.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case plugin.FieldPath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPath(v)
		return nil
	case plugin.FieldBuiltin:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBuiltin(v)
		return nil
	case plugin.FieldEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnabled(v)
		return nil
	case plugin.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case plugin.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Plugin field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PluginMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PluginMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PluginMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Plugin numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PluginMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PluginMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PluginMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Plugin nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PluginMutation) ResetField(name string) error {
	switch name {
	case plugin.FieldName:
		m.ResetName()
		return nil
	case plugin.FieldVersion:
		m.ResetVersion()
		return nil
	case plugin.FieldAuthor:
		m.ResetAuthor()
		return nil
	case plugin.FieldDisplayName:
		m.ResetDisplayName()
		return nil
	case plugin.FieldDescription:
		m.ResetDescription()
		return nil
	case plugin.FieldIcon:
		m.ResetIcon()
		return nil
	case plugin.FieldPath:
		m.ResetPath()
		return nil
	case plugin.FieldBuiltin:
		m.ResetBuiltin()
		return nil
	case plugin.FieldEnabled:
		m.ResetEnabled()
		return nil
	case plugin.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case plugin.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown Plugin field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PluginMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PluginMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PluginMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PluginMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PluginMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PluginMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PluginMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Plugin unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PluginMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Plugin edge %s", name)
}

// UserMutation represents an operation that mutates the User nodes in the graph.
type UserMutation struct {
	config
	op               Op
	typ              string
	id               *uuid.UUID
	username         *string
	password         *string
	nickname         *string
	status           *int
	addstatus        *int
	created_at       *time.Time
	updated_at       *time.Time
	clearedFields    map[string]struct{}
	workflows        map[uuid.UUID]struct{}
	removedworkflows map[uuid.UUID]struct{}
	clearedworkflows bool
	done             bool
	oldValue         func(context.Context) (*User, error)
	predicates       []predicate.User
}

var _ ent.Mutation = (*UserMutation)(nil)

// userOption allows management of the mutation configuration using functional options.
type userOption func(*UserMutation)

// newUserMutation creates new mutation for the User entity.
func newUserMutation(c config, op Op, opts ...userOption) *UserMutation {
	m := &UserMutation{
		config:        c,
		op:            op,
		typ:           TypeUser,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUserID sets the ID field of the mutation.
func withUserID(id uuid.UUID) userOption {
	return func(m *UserMutation) {
		var (
			err   error
			once  sync.Once
			value *User
		)
		m.oldValue = func(ctx context.Context) (*User, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().User.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUser sets the old User of the mutation.
func withUser(node *User) userOption {
	return func(m *UserMutation) {
		m.oldValue = func(context.Context) (*User, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UserMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UserMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of User entities.
func (m *UserMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UserMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UserMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().User.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUsername sets the "username" field.
func (m *UserMutation) SetUsername(s string) {
	m.username = &s
}

// Username returns the value of the "username" field in the mutation.
func (m *UserMutation) Username() (r string, exists bool) {
	v := m.username
	if v == nil {
		return
	}
	return *v, true
}

// OldUsername returns the old "username" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUsername(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUsername is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUsername requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUsername: %w", err)
	}
	return oldValue.Username, nil
}

// ResetUsername resets all changes to the "username" field.
func (m *UserMutation) ResetUsername() {
	m.username = nil
}

// SetPassword sets the "password" field.
func (m *UserMutation) SetPassword(s string) {
	m.password = &s
}

// Password returns the value of the "password" field in the mutation.
func (m *UserMutation) Password() (r string, exists bool) {
	v := m.password
	if v == nil {
		return
	}
	return *v, true
}

// OldPassword returns the old "password" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldPassword(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPassword is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPassword requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPassword: %w", err)
	}
	return oldValue.Password, nil
}

// ResetPassword resets all changes to the "password" field.
func (m *UserMutation) ResetPassword() {
	m.password = nil
}

// SetNickname sets the "nickname" field.
func (m *UserMutation) SetNickname(s string) {
	m.nickname = &s
}

// Nickname returns the value of the "nickname" field in the mutation.
func (m *UserMutation) Nickname() (r string, exists bool) {
	v := m.nickname
	if v == nil {
		return
	}
	return *v, true
}

// OldNickname returns the old "nickname" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldNickname(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNickname is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNickname requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNickname: %w", err)
	}
	return oldValue.Nickname, nil
}

// ClearNickname clears the value of the "nickname" field.
func (m *UserMutation) ClearNickname() {
	m.nickname = nil
	m.clearedFields[user.FieldNickname] = struct{}{}
}

// NicknameCleared returns if the "nickname" field was cleared in this mutation.
func (m *UserMutation) NicknameCleared() bool {
	_, ok := m.clearedFields[user.FieldNickname]
	return ok
}

// ResetNickname resets all changes to the "nickname" field.
func (m *UserMutation) ResetNickname() {
	m.nickname = nil
	delete(m.clearedFields, user.FieldNickname)
}

// SetStatus sets the "status" field.
func (m *UserMutation) SetStatus(i int) {
	m.status = &i
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *UserMutation) Status() (r int, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldStatus(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds i to the "status" field.
func (m *UserMutation) AddStatus(i int) {
	if m.addstatus != nil {
		*m.addstatus += i
	} else {
		m.addstatus = &i
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *UserMutation) AddedStatus() (r int, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ResetStatus resets all changes to the "status" field.
func (m *UserMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *UserMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *UserMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *UserMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *UserMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *UserMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *UserMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// AddWorkflowIDs adds the "workflows" edge to the Workflow entity by ids.
func (m *UserMutation) AddWorkflowIDs(ids ...uuid.UUID) {
	if m.workflows == nil {
		m.workflows = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.workflows[ids[i]] = struct{}{}
	}
}

// ClearWorkflows clears the "workflows" edge to the Workflow entity.
func (m *UserMutation) ClearWorkflows() {
	m.clearedworkflows = true
}

// WorkflowsCleared reports if the "workflows" edge to the Workflow entity was cleared.
func (m *UserMutation) WorkflowsCleared() bool {
	return m.clearedworkflows
}

// RemoveWorkflowIDs removes the "workflows" edge to the Workflow entity by IDs.
func (m *UserMutation) RemoveWorkflowIDs(ids ...uuid.UUID) {
	if m.removedworkflows == nil {
		m.removedworkflows = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.workflows, ids[i])
		m.removedworkflows[ids[i]] = struct{}{}
	}
}

// RemovedWorkflows returns the removed IDs of the "workflows" edge to the Workflow entity.
func (m *UserMutation) RemovedWorkflowsIDs() (ids []uuid.UUID) {
	for id := range m.removedworkflows {
		ids = append(ids, id)
	}
	return
}

// WorkflowsIDs returns the "workflows" edge IDs in the mutation.
func (m *UserMutation) WorkflowsIDs() (ids []uuid.UUID) {
	for id := range m.workflows {
		ids = append(ids, id)
	}
	return
}

// ResetWorkflows resets all changes to the "workflows" edge.
func (m *UserMutation) ResetWorkflows() {
	m.workflows = nil
	m.clearedworkflows = false
	m.removedworkflows = nil
}

// Where appends a list predicates to the UserMutation builder.
func (m *UserMutation) Where(ps ...predicate.User) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UserMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UserMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.User, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UserMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UserMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (User).
func (m *UserMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UserMutation) Fields() []string {
	fields := make([]string, 0, 6)
	if m.username != nil {
		fields = append(fields, user.FieldUsername)
	}
	if m.password != nil {
		fields = append(fields, user.FieldPassword)
	}
	if m.nickname != nil {
		fields = append(fields, user.FieldNickname)
	}
	if m.status != nil {
		fields = append(fields, user.FieldStatus)
	}
	if m.created_at != nil {
		fields = append(fields, user.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, user.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UserMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case user.FieldUsername:
		return m.Username()
	case user.FieldPassword:
		return m.Password()
	case user.FieldNickname:
		return m.Nickname()
	case user.FieldStatus:
		return m.Status()
	case user.FieldCreatedAt:
		return m.CreatedAt()
	case user.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UserMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case user.FieldUsername:
		return m.OldUsername(ctx)
	case user.FieldPassword:
		return m.OldPassword(ctx)
	case user.FieldNickname:
		return m.OldNickname(ctx)
	case user.FieldStatus:
		return m.OldStatus(ctx)
	case user.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case user.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown User field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) SetField(name string, value ent.Value) error {
	switch name {
	case user.FieldUsername:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUsername(v)
		return nil
	case user.FieldPassword:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPassword(v)
		return nil
	case user.FieldNickname:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNickname(v)
		return nil
	case user.FieldStatus:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case user.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case user.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UserMutation) AddedFields() []string {
	var fields []string
	if m.addstatus != nil {
		fields = append(fields, user.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UserMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case user.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) AddField(name string, value ent.Value) error {
	switch name {
	case user.FieldStatus:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown User numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UserMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(user.FieldNickname) {
		fields = append(fields, user.FieldNickname)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UserMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UserMutation) ClearField(name string) error {
	switch name {
	case user.FieldNickname:
		m.ClearNickname()
		return nil
	}
	return fmt.Errorf("unknown User nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UserMutation) ResetField(name string) error {
	switch name {
	case user.FieldUsername:
		m.ResetUsername()
		return nil
	case user.FieldPassword:
		m.ResetPassword()
		return nil
	case user.FieldNickname:
		m.ResetNickname()
		return nil
	case user.FieldStatus:
		m.ResetStatus()
		return nil
	case user.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case user.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UserMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.workflows != nil {
		edges = append(edges, user.EdgeWorkflows)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UserMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeWorkflows:
		ids := make([]ent.Value, 0, len(m.workflows))
		for id := range m.workflows {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UserMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedworkflows != nil {
		edges = append(edges, user.EdgeWorkflows)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UserMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeWorkflows:
		ids := make([]ent.Value, 0, len(m.removedworkflows))
		for id := range m.removedworkflows {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UserMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedworkflows {
		edges = append(edges, user.EdgeWorkflows)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UserMutation) EdgeCleared(name string) bool {
	switch name {
	case user.EdgeWorkflows:
		return m.clearedworkflows
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UserMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown User unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UserMutation) ResetEdge(name string) error {
	switch name {
	case user.EdgeWorkflows:
		m.ResetWorkflows()
		return nil
	}
	return fmt.Errorf("unknown User edge %s", name)
}

// WorkflowMutation represents an operation that mutates the Workflow nodes in the graph.
type WorkflowMutation struct {
	config
	op                    Op
	typ                   string
	id                    *uuid.UUID
	name                  *string
	icon_type             *string
	icon_data             *string
	description           *string
	status                *int
	addstatus             *int
	viewport              *json.RawMessage
	appendviewport        json.RawMessage
	created_at            *time.Time
	updated_at            *time.Time
	clearedFields         map[string]struct{}
	user                  *uuid.UUID
	cleareduser           bool
	nodes                 map[uuid.UUID]struct{}
	removednodes          map[uuid.UUID]struct{}
	clearednodes          bool
	workflow_edges        map[uuid.UUID]struct{}
	removedworkflow_edges map[uuid.UUID]struct{}
	clearedworkflow_edges bool
	done                  bool
	oldValue              func(context.Context) (*Workflow, error)
	predicates            []predicate.Workflow
}

var _ ent.Mutation = (*WorkflowMutation)(nil)

// workflowOption allows management of the mutation configuration using functional options.
type workflowOption func(*WorkflowMutation)

// newWorkflowMutation creates new mutation for the Workflow entity.
func newWorkflowMutation(c config, op Op, opts ...workflowOption) *WorkflowMutation {
	m := &WorkflowMutation{
		config:        c,
		op:            op,
		typ:           TypeWorkflow,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withWorkflowID sets the ID field of the mutation.
func withWorkflowID(id uuid.UUID) workflowOption {
	return func(m *WorkflowMutation) {
		var (
			err   error
			once  sync.Once
			value *Workflow
		)
		m.oldValue = func(ctx context.Context) (*Workflow, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Workflow.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withWorkflow sets the old Workflow of the mutation.
func withWorkflow(node *Workflow) workflowOption {
	return func(m *WorkflowMutation) {
		m.oldValue = func(context.Context) (*Workflow, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m WorkflowMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m WorkflowMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Workflow entities.
func (m *WorkflowMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *WorkflowMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *WorkflowMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Workflow.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *WorkflowMutation) SetUserID(u uuid.UUID) {
	m.user = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *WorkflowMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ClearUserID clears the value of the "user_id" field.
func (m *WorkflowMutation) ClearUserID() {
	m.user = nil
	m.clearedFields[workflow.FieldUserID] = struct{}{}
}

// UserIDCleared returns if the "user_id" field was cleared in this mutation.
func (m *WorkflowMutation) UserIDCleared() bool {
	_, ok := m.clearedFields[workflow.FieldUserID]
	return ok
}

// ResetUserID resets all changes to the "user_id" field.
func (m *WorkflowMutation) ResetUserID() {
	m.user = nil
	delete(m.clearedFields, workflow.FieldUserID)
}

// SetName sets the "name" field.
func (m *WorkflowMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *WorkflowMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *WorkflowMutation) ResetName() {
	m.name = nil
}

// SetIconType sets the "icon_type" field.
func (m *WorkflowMutation) SetIconType(s string) {
	m.icon_type = &s
}

// IconType returns the value of the "icon_type" field in the mutation.
func (m *WorkflowMutation) IconType() (r string, exists bool) {
	v := m.icon_type
	if v == nil {
		return
	}
	return *v, true
}

// OldIconType returns the old "icon_type" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldIconType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIconType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIconType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIconType: %w", err)
	}
	return oldValue.IconType, nil
}

// ResetIconType resets all changes to the "icon_type" field.
func (m *WorkflowMutation) ResetIconType() {
	m.icon_type = nil
}

// SetIconData sets the "icon_data" field.
func (m *WorkflowMutation) SetIconData(s string) {
	m.icon_data = &s
}

// IconData returns the value of the "icon_data" field in the mutation.
func (m *WorkflowMutation) IconData() (r string, exists bool) {
	v := m.icon_data
	if v == nil {
		return
	}
	return *v, true
}

// OldIconData returns the old "icon_data" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldIconData(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIconData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIconData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIconData: %w", err)
	}
	return oldValue.IconData, nil
}

// ResetIconData resets all changes to the "icon_data" field.
func (m *WorkflowMutation) ResetIconData() {
	m.icon_data = nil
}

// SetDescription sets the "description" field.
func (m *WorkflowMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *WorkflowMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *WorkflowMutation) ResetDescription() {
	m.description = nil
}

// SetStatus sets the "status" field.
func (m *WorkflowMutation) SetStatus(i int) {
	m.status = &i
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *WorkflowMutation) Status() (r int, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldStatus(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds i to the "status" field.
func (m *WorkflowMutation) AddStatus(i int) {
	if m.addstatus != nil {
		*m.addstatus += i
	} else {
		m.addstatus = &i
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *WorkflowMutation) AddedStatus() (r int, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ResetStatus resets all changes to the "status" field.
func (m *WorkflowMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
}

// SetViewport sets the "viewport" field.
func (m *WorkflowMutation) SetViewport(jm json.RawMessage) {
	m.viewport = &jm
	m.appendviewport = nil
}

// Viewport returns the value of the "viewport" field in the mutation.
func (m *WorkflowMutation) Viewport() (r json.RawMessage, exists bool) {
	v := m.viewport
	if v == nil {
		return
	}
	return *v, true
}

// OldViewport returns the old "viewport" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldViewport(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldViewport is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldViewport requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldViewport: %w", err)
	}
	return oldValue.Viewport, nil
}

// AppendViewport adds jm to the "viewport" field.
func (m *WorkflowMutation) AppendViewport(jm json.RawMessage) {
	m.appendviewport = append(m.appendviewport, jm...)
}

// AppendedViewport returns the list of values that were appended to the "viewport" field in this mutation.
func (m *WorkflowMutation) AppendedViewport() (json.RawMessage, bool) {
	if len(m.appendviewport) == 0 {
		return nil, false
	}
	return m.appendviewport, true
}

// ResetViewport resets all changes to the "viewport" field.
func (m *WorkflowMutation) ResetViewport() {
	m.viewport = nil
	m.appendviewport = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *WorkflowMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *WorkflowMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *WorkflowMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *WorkflowMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *WorkflowMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *WorkflowMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearUser clears the "user" edge to the User entity.
func (m *WorkflowMutation) ClearUser() {
	m.cleareduser = true
	m.clearedFields[workflow.FieldUserID] = struct{}{}
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *WorkflowMutation) UserCleared() bool {
	return m.UserIDCleared() || m.cleareduser
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *WorkflowMutation) UserIDs() (ids []uuid.UUID) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *WorkflowMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// AddNodeIDs adds the "nodes" edge to the WorkflowNode entity by ids.
func (m *WorkflowMutation) AddNodeIDs(ids ...uuid.UUID) {
	if m.nodes == nil {
		m.nodes = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.nodes[ids[i]] = struct{}{}
	}
}

// ClearNodes clears the "nodes" edge to the WorkflowNode entity.
func (m *WorkflowMutation) ClearNodes() {
	m.clearednodes = true
}

// NodesCleared reports if the "nodes" edge to the WorkflowNode entity was cleared.
func (m *WorkflowMutation) NodesCleared() bool {
	return m.clearednodes
}

// RemoveNodeIDs removes the "nodes" edge to the WorkflowNode entity by IDs.
func (m *WorkflowMutation) RemoveNodeIDs(ids ...uuid.UUID) {
	if m.removednodes == nil {
		m.removednodes = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.nodes, ids[i])
		m.removednodes[ids[i]] = struct{}{}
	}
}

// RemovedNodes returns the removed IDs of the "nodes" edge to the WorkflowNode entity.
func (m *WorkflowMutation) RemovedNodesIDs() (ids []uuid.UUID) {
	for id := range m.removednodes {
		ids = append(ids, id)
	}
	return
}

// NodesIDs returns the "nodes" edge IDs in the mutation.
func (m *WorkflowMutation) NodesIDs() (ids []uuid.UUID) {
	for id := range m.nodes {
		ids = append(ids, id)
	}
	return
}

// ResetNodes resets all changes to the "nodes" edge.
func (m *WorkflowMutation) ResetNodes() {
	m.nodes = nil
	m.clearednodes = false
	m.removednodes = nil
}

// AddWorkflowEdgeIDs adds the "workflow_edges" edge to the WorkflowsEdge entity by ids.
func (m *WorkflowMutation) AddWorkflowEdgeIDs(ids ...uuid.UUID) {
	if m.workflow_edges == nil {
		m.workflow_edges = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.workflow_edges[ids[i]] = struct{}{}
	}
}

// ClearWorkflowEdges clears the "workflow_edges" edge to the WorkflowsEdge entity.
func (m *WorkflowMutation) ClearWorkflowEdges() {
	m.clearedworkflow_edges = true
}

// WorkflowEdgesCleared reports if the "workflow_edges" edge to the WorkflowsEdge entity was cleared.
func (m *WorkflowMutation) WorkflowEdgesCleared() bool {
	return m.clearedworkflow_edges
}

// RemoveWorkflowEdgeIDs removes the "workflow_edges" edge to the WorkflowsEdge entity by IDs.
func (m *WorkflowMutation) RemoveWorkflowEdgeIDs(ids ...uuid.UUID) {
	if m.removedworkflow_edges == nil {
		m.removedworkflow_edges = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.workflow_edges, ids[i])
		m.removedworkflow_edges[ids[i]] = struct{}{}
	}
}

// RemovedWorkflowEdges returns the removed IDs of the "workflow_edges" edge to the WorkflowsEdge entity.
func (m *WorkflowMutation) RemovedWorkflowEdgesIDs() (ids []uuid.UUID) {
	for id := range m.removedworkflow_edges {
		ids = append(ids, id)
	}
	return
}

// WorkflowEdgesIDs returns the "workflow_edges" edge IDs in the mutation.
func (m *WorkflowMutation) WorkflowEdgesIDs() (ids []uuid.UUID) {
	for id := range m.workflow_edges {
		ids = append(ids, id)
	}
	return
}

// ResetWorkflowEdges resets all changes to the "workflow_edges" edge.
func (m *WorkflowMutation) ResetWorkflowEdges() {
	m.workflow_edges = nil
	m.clearedworkflow_edges = false
	m.removedworkflow_edges = nil
}

// Where appends a list predicates to the WorkflowMutation builder.
func (m *WorkflowMutation) Where(ps ...predicate.Workflow) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the WorkflowMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *WorkflowMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Workflow, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *WorkflowMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *WorkflowMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Workflow).
func (m *WorkflowMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *WorkflowMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.user != nil {
		fields = append(fields, workflow.FieldUserID)
	}
	if m.name != nil {
		fields = append(fields, workflow.FieldName)
	}
	if m.icon_type != nil {
		fields = append(fields, workflow.FieldIconType)
	}
	if m.icon_data != nil {
		fields = append(fields, workflow.FieldIconData)
	}
	if m.description != nil {
		fields = append(fields, workflow.FieldDescription)
	}
	if m.status != nil {
		fields = append(fields, workflow.FieldStatus)
	}
	if m.viewport != nil {
		fields = append(fields, workflow.FieldViewport)
	}
	if m.created_at != nil {
		fields = append(fields, workflow.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, workflow.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *WorkflowMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case workflow.FieldUserID:
		return m.UserID()
	case workflow.FieldName:
		return m.Name()
	case workflow.FieldIconType:
		return m.IconType()
	case workflow.FieldIconData:
		return m.IconData()
	case workflow.FieldDescription:
		return m.Description()
	case workflow.FieldStatus:
		return m.Status()
	case workflow.FieldViewport:
		return m.Viewport()
	case workflow.FieldCreatedAt:
		return m.CreatedAt()
	case workflow.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *WorkflowMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case workflow.FieldUserID:
		return m.OldUserID(ctx)
	case workflow.FieldName:
		return m.OldName(ctx)
	case workflow.FieldIconType:
		return m.OldIconType(ctx)
	case workflow.FieldIconData:
		return m.OldIconData(ctx)
	case workflow.FieldDescription:
		return m.OldDescription(ctx)
	case workflow.FieldStatus:
		return m.OldStatus(ctx)
	case workflow.FieldViewport:
		return m.OldViewport(ctx)
	case workflow.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case workflow.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Workflow field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowMutation) SetField(name string, value ent.Value) error {
	switch name {
	case workflow.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case workflow.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case workflow.FieldIconType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIconType(v)
		return nil
	case workflow.FieldIconData:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIconData(v)
		return nil
	case workflow.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case workflow.FieldStatus:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case workflow.FieldViewport:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetViewport(v)
		return nil
	case workflow.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case workflow.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Workflow field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *WorkflowMutation) AddedFields() []string {
	var fields []string
	if m.addstatus != nil {
		fields = append(fields, workflow.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *WorkflowMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case workflow.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowMutation) AddField(name string, value ent.Value) error {
	switch name {
	case workflow.FieldStatus:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown Workflow numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *WorkflowMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(workflow.FieldUserID) {
		fields = append(fields, workflow.FieldUserID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *WorkflowMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *WorkflowMutation) ClearField(name string) error {
	switch name {
	case workflow.FieldUserID:
		m.ClearUserID()
		return nil
	}
	return fmt.Errorf("unknown Workflow nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *WorkflowMutation) ResetField(name string) error {
	switch name {
	case workflow.FieldUserID:
		m.ResetUserID()
		return nil
	case workflow.FieldName:
		m.ResetName()
		return nil
	case workflow.FieldIconType:
		m.ResetIconType()
		return nil
	case workflow.FieldIconData:
		m.ResetIconData()
		return nil
	case workflow.FieldDescription:
		m.ResetDescription()
		return nil
	case workflow.FieldStatus:
		m.ResetStatus()
		return nil
	case workflow.FieldViewport:
		m.ResetViewport()
		return nil
	case workflow.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case workflow.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown Workflow field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *WorkflowMutation) AddedEdges() []string {
	edges := make([]string, 0, 3)
	if m.user != nil {
		edges = append(edges, workflow.EdgeUser)
	}
	if m.nodes != nil {
		edges = append(edges, workflow.EdgeNodes)
	}
	if m.workflow_edges != nil {
		edges = append(edges, workflow.EdgeWorkflowEdges)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *WorkflowMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case workflow.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	case workflow.EdgeNodes:
		ids := make([]ent.Value, 0, len(m.nodes))
		for id := range m.nodes {
			ids = append(ids, id)
		}
		return ids
	case workflow.EdgeWorkflowEdges:
		ids := make([]ent.Value, 0, len(m.workflow_edges))
		for id := range m.workflow_edges {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *WorkflowMutation) RemovedEdges() []string {
	edges := make([]string, 0, 3)
	if m.removednodes != nil {
		edges = append(edges, workflow.EdgeNodes)
	}
	if m.removedworkflow_edges != nil {
		edges = append(edges, workflow.EdgeWorkflowEdges)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *WorkflowMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case workflow.EdgeNodes:
		ids := make([]ent.Value, 0, len(m.removednodes))
		for id := range m.removednodes {
			ids = append(ids, id)
		}
		return ids
	case workflow.EdgeWorkflowEdges:
		ids := make([]ent.Value, 0, len(m.removedworkflow_edges))
		for id := range m.removedworkflow_edges {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *WorkflowMutation) ClearedEdges() []string {
	edges := make([]string, 0, 3)
	if m.cleareduser {
		edges = append(edges, workflow.EdgeUser)
	}
	if m.clearednodes {
		edges = append(edges, workflow.EdgeNodes)
	}
	if m.clearedworkflow_edges {
		edges = append(edges, workflow.EdgeWorkflowEdges)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *WorkflowMutation) EdgeCleared(name string) bool {
	switch name {
	case workflow.EdgeUser:
		return m.cleareduser
	case workflow.EdgeNodes:
		return m.clearednodes
	case workflow.EdgeWorkflowEdges:
		return m.clearedworkflow_edges
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *WorkflowMutation) ClearEdge(name string) error {
	switch name {
	case workflow.EdgeUser:
		m.ClearUser()
		return nil
	}
	return fmt.Errorf("unknown Workflow unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *WorkflowMutation) ResetEdge(name string) error {
	switch name {
	case workflow.EdgeUser:
		m.ResetUser()
		return nil
	case workflow.EdgeNodes:
		m.ResetNodes()
		return nil
	case workflow.EdgeWorkflowEdges:
		m.ResetWorkflowEdges()
		return nil
	}
	return fmt.Errorf("unknown Workflow edge %s", name)
}

// WorkflowNodeMutation represents an operation that mutates the WorkflowNode nodes in the graph.
type WorkflowNodeMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	name                *string
	description         *string
	icon_type           *string
	icon_data           *string
	_type               *string
	version             *string
	plugin_name         *string
	plugin_version      *string
	input_params        *json.RawMessage
	appendinput_params  json.RawMessage
	output_params       *json.RawMessage
	appendoutput_params json.RawMessage
	input_ports         *json.RawMessage
	appendinput_ports   json.RawMessage
	output_ports        *json.RawMessage
	appendoutput_ports  json.RawMessage
	position            *json.RawMessage
	appendposition      json.RawMessage
	config_data         *json.RawMessage
	appendconfig_data   json.RawMessage
	created_at          *time.Time
	updated_at          *time.Time
	clearedFields       map[string]struct{}
	workflow            *uuid.UUID
	clearedworkflow     bool
	done                bool
	oldValue            func(context.Context) (*WorkflowNode, error)
	predicates          []predicate.WorkflowNode
}

var _ ent.Mutation = (*WorkflowNodeMutation)(nil)

// workflownodeOption allows management of the mutation configuration using functional options.
type workflownodeOption func(*WorkflowNodeMutation)

// newWorkflowNodeMutation creates new mutation for the WorkflowNode entity.
func newWorkflowNodeMutation(c config, op Op, opts ...workflownodeOption) *WorkflowNodeMutation {
	m := &WorkflowNodeMutation{
		config:        c,
		op:            op,
		typ:           TypeWorkflowNode,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withWorkflowNodeID sets the ID field of the mutation.
func withWorkflowNodeID(id uuid.UUID) workflownodeOption {
	return func(m *WorkflowNodeMutation) {
		var (
			err   error
			once  sync.Once
			value *WorkflowNode
		)
		m.oldValue = func(ctx context.Context) (*WorkflowNode, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().WorkflowNode.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withWorkflowNode sets the old WorkflowNode of the mutation.
func withWorkflowNode(node *WorkflowNode) workflownodeOption {
	return func(m *WorkflowNodeMutation) {
		m.oldValue = func(context.Context) (*WorkflowNode, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m WorkflowNodeMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m WorkflowNodeMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of WorkflowNode entities.
func (m *WorkflowNodeMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *WorkflowNodeMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *WorkflowNodeMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().WorkflowNode.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetWorkflowID sets the "workflow_id" field.
func (m *WorkflowNodeMutation) SetWorkflowID(u uuid.UUID) {
	m.workflow = &u
}

// WorkflowID returns the value of the "workflow_id" field in the mutation.
func (m *WorkflowNodeMutation) WorkflowID() (r uuid.UUID, exists bool) {
	v := m.workflow
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkflowID returns the old "workflow_id" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldWorkflowID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkflowID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkflowID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkflowID: %w", err)
	}
	return oldValue.WorkflowID, nil
}

// ResetWorkflowID resets all changes to the "workflow_id" field.
func (m *WorkflowNodeMutation) ResetWorkflowID() {
	m.workflow = nil
}

// SetName sets the "name" field.
func (m *WorkflowNodeMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *WorkflowNodeMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *WorkflowNodeMutation) ResetName() {
	m.name = nil
}

// SetDescription sets the "description" field.
func (m *WorkflowNodeMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *WorkflowNodeMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *WorkflowNodeMutation) ResetDescription() {
	m.description = nil
}

// SetIconType sets the "icon_type" field.
func (m *WorkflowNodeMutation) SetIconType(s string) {
	m.icon_type = &s
}

// IconType returns the value of the "icon_type" field in the mutation.
func (m *WorkflowNodeMutation) IconType() (r string, exists bool) {
	v := m.icon_type
	if v == nil {
		return
	}
	return *v, true
}

// OldIconType returns the old "icon_type" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldIconType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIconType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIconType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIconType: %w", err)
	}
	return oldValue.IconType, nil
}

// ResetIconType resets all changes to the "icon_type" field.
func (m *WorkflowNodeMutation) ResetIconType() {
	m.icon_type = nil
}

// SetIconData sets the "icon_data" field.
func (m *WorkflowNodeMutation) SetIconData(s string) {
	m.icon_data = &s
}

// IconData returns the value of the "icon_data" field in the mutation.
func (m *WorkflowNodeMutation) IconData() (r string, exists bool) {
	v := m.icon_data
	if v == nil {
		return
	}
	return *v, true
}

// OldIconData returns the old "icon_data" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldIconData(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIconData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIconData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIconData: %w", err)
	}
	return oldValue.IconData, nil
}

// ResetIconData resets all changes to the "icon_data" field.
func (m *WorkflowNodeMutation) ResetIconData() {
	m.icon_data = nil
}

// SetType sets the "type" field.
func (m *WorkflowNodeMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *WorkflowNodeMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *WorkflowNodeMutation) ResetType() {
	m._type = nil
}

// SetVersion sets the "version" field.
func (m *WorkflowNodeMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *WorkflowNodeMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *WorkflowNodeMutation) ResetVersion() {
	m.version = nil
}

// SetPluginName sets the "plugin_name" field.
func (m *WorkflowNodeMutation) SetPluginName(s string) {
	m.plugin_name = &s
}

// PluginName returns the value of the "plugin_name" field in the mutation.
func (m *WorkflowNodeMutation) PluginName() (r string, exists bool) {
	v := m.plugin_name
	if v == nil {
		return
	}
	return *v, true
}

// OldPluginName returns the old "plugin_name" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldPluginName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPluginName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPluginName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPluginName: %w", err)
	}
	return oldValue.PluginName, nil
}

// ResetPluginName resets all changes to the "plugin_name" field.
func (m *WorkflowNodeMutation) ResetPluginName() {
	m.plugin_name = nil
}

// SetPluginVersion sets the "plugin_version" field.
func (m *WorkflowNodeMutation) SetPluginVersion(s string) {
	m.plugin_version = &s
}

// PluginVersion returns the value of the "plugin_version" field in the mutation.
func (m *WorkflowNodeMutation) PluginVersion() (r string, exists bool) {
	v := m.plugin_version
	if v == nil {
		return
	}
	return *v, true
}

// OldPluginVersion returns the old "plugin_version" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldPluginVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPluginVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPluginVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPluginVersion: %w", err)
	}
	return oldValue.PluginVersion, nil
}

// ResetPluginVersion resets all changes to the "plugin_version" field.
func (m *WorkflowNodeMutation) ResetPluginVersion() {
	m.plugin_version = nil
}

// SetInputParams sets the "input_params" field.
func (m *WorkflowNodeMutation) SetInputParams(jm json.RawMessage) {
	m.input_params = &jm
	m.appendinput_params = nil
}

// InputParams returns the value of the "input_params" field in the mutation.
func (m *WorkflowNodeMutation) InputParams() (r json.RawMessage, exists bool) {
	v := m.input_params
	if v == nil {
		return
	}
	return *v, true
}

// OldInputParams returns the old "input_params" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldInputParams(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputParams: %w", err)
	}
	return oldValue.InputParams, nil
}

// AppendInputParams adds jm to the "input_params" field.
func (m *WorkflowNodeMutation) AppendInputParams(jm json.RawMessage) {
	m.appendinput_params = append(m.appendinput_params, jm...)
}

// AppendedInputParams returns the list of values that were appended to the "input_params" field in this mutation.
func (m *WorkflowNodeMutation) AppendedInputParams() (json.RawMessage, bool) {
	if len(m.appendinput_params) == 0 {
		return nil, false
	}
	return m.appendinput_params, true
}

// ResetInputParams resets all changes to the "input_params" field.
func (m *WorkflowNodeMutation) ResetInputParams() {
	m.input_params = nil
	m.appendinput_params = nil
}

// SetOutputParams sets the "output_params" field.
func (m *WorkflowNodeMutation) SetOutputParams(jm json.RawMessage) {
	m.output_params = &jm
	m.appendoutput_params = nil
}

// OutputParams returns the value of the "output_params" field in the mutation.
func (m *WorkflowNodeMutation) OutputParams() (r json.RawMessage, exists bool) {
	v := m.output_params
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputParams returns the old "output_params" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldOutputParams(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputParams: %w", err)
	}
	return oldValue.OutputParams, nil
}

// AppendOutputParams adds jm to the "output_params" field.
func (m *WorkflowNodeMutation) AppendOutputParams(jm json.RawMessage) {
	m.appendoutput_params = append(m.appendoutput_params, jm...)
}

// AppendedOutputParams returns the list of values that were appended to the "output_params" field in this mutation.
func (m *WorkflowNodeMutation) AppendedOutputParams() (json.RawMessage, bool) {
	if len(m.appendoutput_params) == 0 {
		return nil, false
	}
	return m.appendoutput_params, true
}

// ResetOutputParams resets all changes to the "output_params" field.
func (m *WorkflowNodeMutation) ResetOutputParams() {
	m.output_params = nil
	m.appendoutput_params = nil
}

// SetInputPorts sets the "input_ports" field.
func (m *WorkflowNodeMutation) SetInputPorts(jm json.RawMessage) {
	m.input_ports = &jm
	m.appendinput_ports = nil
}

// InputPorts returns the value of the "input_ports" field in the mutation.
func (m *WorkflowNodeMutation) InputPorts() (r json.RawMessage, exists bool) {
	v := m.input_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldInputPorts returns the old "input_ports" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldInputPorts(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputPorts: %w", err)
	}
	return oldValue.InputPorts, nil
}

// AppendInputPorts adds jm to the "input_ports" field.
func (m *WorkflowNodeMutation) AppendInputPorts(jm json.RawMessage) {
	m.appendinput_ports = append(m.appendinput_ports, jm...)
}

// AppendedInputPorts returns the list of values that were appended to the "input_ports" field in this mutation.
func (m *WorkflowNodeMutation) AppendedInputPorts() (json.RawMessage, bool) {
	if len(m.appendinput_ports) == 0 {
		return nil, false
	}
	return m.appendinput_ports, true
}

// ResetInputPorts resets all changes to the "input_ports" field.
func (m *WorkflowNodeMutation) ResetInputPorts() {
	m.input_ports = nil
	m.appendinput_ports = nil
}

// SetOutputPorts sets the "output_ports" field.
func (m *WorkflowNodeMutation) SetOutputPorts(jm json.RawMessage) {
	m.output_ports = &jm
	m.appendoutput_ports = nil
}

// OutputPorts returns the value of the "output_ports" field in the mutation.
func (m *WorkflowNodeMutation) OutputPorts() (r json.RawMessage, exists bool) {
	v := m.output_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputPorts returns the old "output_ports" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldOutputPorts(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputPorts: %w", err)
	}
	return oldValue.OutputPorts, nil
}

// AppendOutputPorts adds jm to the "output_ports" field.
func (m *WorkflowNodeMutation) AppendOutputPorts(jm json.RawMessage) {
	m.appendoutput_ports = append(m.appendoutput_ports, jm...)
}

// AppendedOutputPorts returns the list of values that were appended to the "output_ports" field in this mutation.
func (m *WorkflowNodeMutation) AppendedOutputPorts() (json.RawMessage, bool) {
	if len(m.appendoutput_ports) == 0 {
		return nil, false
	}
	return m.appendoutput_ports, true
}

// ResetOutputPorts resets all changes to the "output_ports" field.
func (m *WorkflowNodeMutation) ResetOutputPorts() {
	m.output_ports = nil
	m.appendoutput_ports = nil
}

// SetPosition sets the "position" field.
func (m *WorkflowNodeMutation) SetPosition(jm json.RawMessage) {
	m.position = &jm
	m.appendposition = nil
}

// Position returns the value of the "position" field in the mutation.
func (m *WorkflowNodeMutation) Position() (r json.RawMessage, exists bool) {
	v := m.position
	if v == nil {
		return
	}
	return *v, true
}

// OldPosition returns the old "position" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldPosition(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPosition is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPosition requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPosition: %w", err)
	}
	return oldValue.Position, nil
}

// AppendPosition adds jm to the "position" field.
func (m *WorkflowNodeMutation) AppendPosition(jm json.RawMessage) {
	m.appendposition = append(m.appendposition, jm...)
}

// AppendedPosition returns the list of values that were appended to the "position" field in this mutation.
func (m *WorkflowNodeMutation) AppendedPosition() (json.RawMessage, bool) {
	if len(m.appendposition) == 0 {
		return nil, false
	}
	return m.appendposition, true
}

// ResetPosition resets all changes to the "position" field.
func (m *WorkflowNodeMutation) ResetPosition() {
	m.position = nil
	m.appendposition = nil
}

// SetConfigData sets the "config_data" field.
func (m *WorkflowNodeMutation) SetConfigData(jm json.RawMessage) {
	m.config_data = &jm
	m.appendconfig_data = nil
}

// ConfigData returns the value of the "config_data" field in the mutation.
func (m *WorkflowNodeMutation) ConfigData() (r json.RawMessage, exists bool) {
	v := m.config_data
	if v == nil {
		return
	}
	return *v, true
}

// OldConfigData returns the old "config_data" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldConfigData(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfigData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfigData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfigData: %w", err)
	}
	return oldValue.ConfigData, nil
}

// AppendConfigData adds jm to the "config_data" field.
func (m *WorkflowNodeMutation) AppendConfigData(jm json.RawMessage) {
	m.appendconfig_data = append(m.appendconfig_data, jm...)
}

// AppendedConfigData returns the list of values that were appended to the "config_data" field in this mutation.
func (m *WorkflowNodeMutation) AppendedConfigData() (json.RawMessage, bool) {
	if len(m.appendconfig_data) == 0 {
		return nil, false
	}
	return m.appendconfig_data, true
}

// ClearConfigData clears the value of the "config_data" field.
func (m *WorkflowNodeMutation) ClearConfigData() {
	m.config_data = nil
	m.appendconfig_data = nil
	m.clearedFields[workflownode.FieldConfigData] = struct{}{}
}

// ConfigDataCleared returns if the "config_data" field was cleared in this mutation.
func (m *WorkflowNodeMutation) ConfigDataCleared() bool {
	_, ok := m.clearedFields[workflownode.FieldConfigData]
	return ok
}

// ResetConfigData resets all changes to the "config_data" field.
func (m *WorkflowNodeMutation) ResetConfigData() {
	m.config_data = nil
	m.appendconfig_data = nil
	delete(m.clearedFields, workflownode.FieldConfigData)
}

// SetCreatedAt sets the "created_at" field.
func (m *WorkflowNodeMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *WorkflowNodeMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *WorkflowNodeMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *WorkflowNodeMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *WorkflowNodeMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *WorkflowNodeMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (m *WorkflowNodeMutation) ClearWorkflow() {
	m.clearedworkflow = true
	m.clearedFields[workflownode.FieldWorkflowID] = struct{}{}
}

// WorkflowCleared reports if the "workflow" edge to the Workflow entity was cleared.
func (m *WorkflowNodeMutation) WorkflowCleared() bool {
	return m.clearedworkflow
}

// WorkflowIDs returns the "workflow" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// WorkflowID instead. It exists only for internal usage by the builders.
func (m *WorkflowNodeMutation) WorkflowIDs() (ids []uuid.UUID) {
	if id := m.workflow; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetWorkflow resets all changes to the "workflow" edge.
func (m *WorkflowNodeMutation) ResetWorkflow() {
	m.workflow = nil
	m.clearedworkflow = false
}

// Where appends a list predicates to the WorkflowNodeMutation builder.
func (m *WorkflowNodeMutation) Where(ps ...predicate.WorkflowNode) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the WorkflowNodeMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *WorkflowNodeMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.WorkflowNode, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *WorkflowNodeMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *WorkflowNodeMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (WorkflowNode).
func (m *WorkflowNodeMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *WorkflowNodeMutation) Fields() []string {
	fields := make([]string, 0, 17)
	if m.workflow != nil {
		fields = append(fields, workflownode.FieldWorkflowID)
	}
	if m.name != nil {
		fields = append(fields, workflownode.FieldName)
	}
	if m.description != nil {
		fields = append(fields, workflownode.FieldDescription)
	}
	if m.icon_type != nil {
		fields = append(fields, workflownode.FieldIconType)
	}
	if m.icon_data != nil {
		fields = append(fields, workflownode.FieldIconData)
	}
	if m._type != nil {
		fields = append(fields, workflownode.FieldType)
	}
	if m.version != nil {
		fields = append(fields, workflownode.FieldVersion)
	}
	if m.plugin_name != nil {
		fields = append(fields, workflownode.FieldPluginName)
	}
	if m.plugin_version != nil {
		fields = append(fields, workflownode.FieldPluginVersion)
	}
	if m.input_params != nil {
		fields = append(fields, workflownode.FieldInputParams)
	}
	if m.output_params != nil {
		fields = append(fields, workflownode.FieldOutputParams)
	}
	if m.input_ports != nil {
		fields = append(fields, workflownode.FieldInputPorts)
	}
	if m.output_ports != nil {
		fields = append(fields, workflownode.FieldOutputPorts)
	}
	if m.position != nil {
		fields = append(fields, workflownode.FieldPosition)
	}
	if m.config_data != nil {
		fields = append(fields, workflownode.FieldConfigData)
	}
	if m.created_at != nil {
		fields = append(fields, workflownode.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, workflownode.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *WorkflowNodeMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case workflownode.FieldWorkflowID:
		return m.WorkflowID()
	case workflownode.FieldName:
		return m.Name()
	case workflownode.FieldDescription:
		return m.Description()
	case workflownode.FieldIconType:
		return m.IconType()
	case workflownode.FieldIconData:
		return m.IconData()
	case workflownode.FieldType:
		return m.GetType()
	case workflownode.FieldVersion:
		return m.Version()
	case workflownode.FieldPluginName:
		return m.PluginName()
	case workflownode.FieldPluginVersion:
		return m.PluginVersion()
	case workflownode.FieldInputParams:
		return m.InputParams()
	case workflownode.FieldOutputParams:
		return m.OutputParams()
	case workflownode.FieldInputPorts:
		return m.InputPorts()
	case workflownode.FieldOutputPorts:
		return m.OutputPorts()
	case workflownode.FieldPosition:
		return m.Position()
	case workflownode.FieldConfigData:
		return m.ConfigData()
	case workflownode.FieldCreatedAt:
		return m.CreatedAt()
	case workflownode.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *WorkflowNodeMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case workflownode.FieldWorkflowID:
		return m.OldWorkflowID(ctx)
	case workflownode.FieldName:
		return m.OldName(ctx)
	case workflownode.FieldDescription:
		return m.OldDescription(ctx)
	case workflownode.FieldIconType:
		return m.OldIconType(ctx)
	case workflownode.FieldIconData:
		return m.OldIconData(ctx)
	case workflownode.FieldType:
		return m.OldType(ctx)
	case workflownode.FieldVersion:
		return m.OldVersion(ctx)
	case workflownode.FieldPluginName:
		return m.OldPluginName(ctx)
	case workflownode.FieldPluginVersion:
		return m.OldPluginVersion(ctx)
	case workflownode.FieldInputParams:
		return m.OldInputParams(ctx)
	case workflownode.FieldOutputParams:
		return m.OldOutputParams(ctx)
	case workflownode.FieldInputPorts:
		return m.OldInputPorts(ctx)
	case workflownode.FieldOutputPorts:
		return m.OldOutputPorts(ctx)
	case workflownode.FieldPosition:
		return m.OldPosition(ctx)
	case workflownode.FieldConfigData:
		return m.OldConfigData(ctx)
	case workflownode.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case workflownode.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown WorkflowNode field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowNodeMutation) SetField(name string, value ent.Value) error {
	switch name {
	case workflownode.FieldWorkflowID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkflowID(v)
		return nil
	case workflownode.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case workflownode.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case workflownode.FieldIconType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIconType(v)
		return nil
	case workflownode.FieldIconData:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIconData(v)
		return nil
	case workflownode.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case workflownode.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case workflownode.FieldPluginName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPluginName(v)
		return nil
	case workflownode.FieldPluginVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPluginVersion(v)
		return nil
	case workflownode.FieldInputParams:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputParams(v)
		return nil
	case workflownode.FieldOutputParams:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputParams(v)
		return nil
	case workflownode.FieldInputPorts:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputPorts(v)
		return nil
	case workflownode.FieldOutputPorts:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputPorts(v)
		return nil
	case workflownode.FieldPosition:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPosition(v)
		return nil
	case workflownode.FieldConfigData:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfigData(v)
		return nil
	case workflownode.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case workflownode.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *WorkflowNodeMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *WorkflowNodeMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowNodeMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown WorkflowNode numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *WorkflowNodeMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(workflownode.FieldConfigData) {
		fields = append(fields, workflownode.FieldConfigData)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *WorkflowNodeMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *WorkflowNodeMutation) ClearField(name string) error {
	switch name {
	case workflownode.FieldConfigData:
		m.ClearConfigData()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *WorkflowNodeMutation) ResetField(name string) error {
	switch name {
	case workflownode.FieldWorkflowID:
		m.ResetWorkflowID()
		return nil
	case workflownode.FieldName:
		m.ResetName()
		return nil
	case workflownode.FieldDescription:
		m.ResetDescription()
		return nil
	case workflownode.FieldIconType:
		m.ResetIconType()
		return nil
	case workflownode.FieldIconData:
		m.ResetIconData()
		return nil
	case workflownode.FieldType:
		m.ResetType()
		return nil
	case workflownode.FieldVersion:
		m.ResetVersion()
		return nil
	case workflownode.FieldPluginName:
		m.ResetPluginName()
		return nil
	case workflownode.FieldPluginVersion:
		m.ResetPluginVersion()
		return nil
	case workflownode.FieldInputParams:
		m.ResetInputParams()
		return nil
	case workflownode.FieldOutputParams:
		m.ResetOutputParams()
		return nil
	case workflownode.FieldInputPorts:
		m.ResetInputPorts()
		return nil
	case workflownode.FieldOutputPorts:
		m.ResetOutputPorts()
		return nil
	case workflownode.FieldPosition:
		m.ResetPosition()
		return nil
	case workflownode.FieldConfigData:
		m.ResetConfigData()
		return nil
	case workflownode.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case workflownode.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *WorkflowNodeMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.workflow != nil {
		edges = append(edges, workflownode.EdgeWorkflow)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *WorkflowNodeMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case workflownode.EdgeWorkflow:
		if id := m.workflow; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *WorkflowNodeMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *WorkflowNodeMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *WorkflowNodeMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedworkflow {
		edges = append(edges, workflownode.EdgeWorkflow)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *WorkflowNodeMutation) EdgeCleared(name string) bool {
	switch name {
	case workflownode.EdgeWorkflow:
		return m.clearedworkflow
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *WorkflowNodeMutation) ClearEdge(name string) error {
	switch name {
	case workflownode.EdgeWorkflow:
		m.ClearWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *WorkflowNodeMutation) ResetEdge(name string) error {
	switch name {
	case workflownode.EdgeWorkflow:
		m.ResetWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode edge %s", name)
}

// WorkflowsEdgeMutation represents an operation that mutates the WorkflowsEdge nodes in the graph.
type WorkflowsEdgeMutation struct {
	config
	op              Op
	typ             string
	id              *uuid.UUID
	from_node_id    *uuid.UUID
	to_node_id      *uuid.UUID
	from_port_id    *string
	to_port_id      *string
	_type           *string
	created_at      *time.Time
	updated_at      *time.Time
	clearedFields   map[string]struct{}
	workflow        *uuid.UUID
	clearedworkflow bool
	done            bool
	oldValue        func(context.Context) (*WorkflowsEdge, error)
	predicates      []predicate.WorkflowsEdge
}

var _ ent.Mutation = (*WorkflowsEdgeMutation)(nil)

// workflowsedgeOption allows management of the mutation configuration using functional options.
type workflowsedgeOption func(*WorkflowsEdgeMutation)

// newWorkflowsEdgeMutation creates new mutation for the WorkflowsEdge entity.
func newWorkflowsEdgeMutation(c config, op Op, opts ...workflowsedgeOption) *WorkflowsEdgeMutation {
	m := &WorkflowsEdgeMutation{
		config:        c,
		op:            op,
		typ:           TypeWorkflowsEdge,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withWorkflowsEdgeID sets the ID field of the mutation.
func withWorkflowsEdgeID(id uuid.UUID) workflowsedgeOption {
	return func(m *WorkflowsEdgeMutation) {
		var (
			err   error
			once  sync.Once
			value *WorkflowsEdge
		)
		m.oldValue = func(ctx context.Context) (*WorkflowsEdge, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().WorkflowsEdge.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withWorkflowsEdge sets the old WorkflowsEdge of the mutation.
func withWorkflowsEdge(node *WorkflowsEdge) workflowsedgeOption {
	return func(m *WorkflowsEdgeMutation) {
		m.oldValue = func(context.Context) (*WorkflowsEdge, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m WorkflowsEdgeMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m WorkflowsEdgeMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of WorkflowsEdge entities.
func (m *WorkflowsEdgeMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *WorkflowsEdgeMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *WorkflowsEdgeMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().WorkflowsEdge.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetWorkflowID sets the "workflow_id" field.
func (m *WorkflowsEdgeMutation) SetWorkflowID(u uuid.UUID) {
	m.workflow = &u
}

// WorkflowID returns the value of the "workflow_id" field in the mutation.
func (m *WorkflowsEdgeMutation) WorkflowID() (r uuid.UUID, exists bool) {
	v := m.workflow
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkflowID returns the old "workflow_id" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldWorkflowID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkflowID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkflowID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkflowID: %w", err)
	}
	return oldValue.WorkflowID, nil
}

// ResetWorkflowID resets all changes to the "workflow_id" field.
func (m *WorkflowsEdgeMutation) ResetWorkflowID() {
	m.workflow = nil
}

// SetFromNodeID sets the "from_node_id" field.
func (m *WorkflowsEdgeMutation) SetFromNodeID(u uuid.UUID) {
	m.from_node_id = &u
}

// FromNodeID returns the value of the "from_node_id" field in the mutation.
func (m *WorkflowsEdgeMutation) FromNodeID() (r uuid.UUID, exists bool) {
	v := m.from_node_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFromNodeID returns the old "from_node_id" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldFromNodeID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFromNodeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFromNodeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFromNodeID: %w", err)
	}
	return oldValue.FromNodeID, nil
}

// ResetFromNodeID resets all changes to the "from_node_id" field.
func (m *WorkflowsEdgeMutation) ResetFromNodeID() {
	m.from_node_id = nil
}

// SetToNodeID sets the "to_node_id" field.
func (m *WorkflowsEdgeMutation) SetToNodeID(u uuid.UUID) {
	m.to_node_id = &u
}

// ToNodeID returns the value of the "to_node_id" field in the mutation.
func (m *WorkflowsEdgeMutation) ToNodeID() (r uuid.UUID, exists bool) {
	v := m.to_node_id
	if v == nil {
		return
	}
	return *v, true
}

// OldToNodeID returns the old "to_node_id" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldToNodeID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldToNodeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldToNodeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldToNodeID: %w", err)
	}
	return oldValue.ToNodeID, nil
}

// ResetToNodeID resets all changes to the "to_node_id" field.
func (m *WorkflowsEdgeMutation) ResetToNodeID() {
	m.to_node_id = nil
}

// SetFromPortID sets the "from_port_id" field.
func (m *WorkflowsEdgeMutation) SetFromPortID(s string) {
	m.from_port_id = &s
}

// FromPortID returns the value of the "from_port_id" field in the mutation.
func (m *WorkflowsEdgeMutation) FromPortID() (r string, exists bool) {
	v := m.from_port_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFromPortID returns the old "from_port_id" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldFromPortID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFromPortID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFromPortID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFromPortID: %w", err)
	}
	return oldValue.FromPortID, nil
}

// ResetFromPortID resets all changes to the "from_port_id" field.
func (m *WorkflowsEdgeMutation) ResetFromPortID() {
	m.from_port_id = nil
}

// SetToPortID sets the "to_port_id" field.
func (m *WorkflowsEdgeMutation) SetToPortID(s string) {
	m.to_port_id = &s
}

// ToPortID returns the value of the "to_port_id" field in the mutation.
func (m *WorkflowsEdgeMutation) ToPortID() (r string, exists bool) {
	v := m.to_port_id
	if v == nil {
		return
	}
	return *v, true
}

// OldToPortID returns the old "to_port_id" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldToPortID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldToPortID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldToPortID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldToPortID: %w", err)
	}
	return oldValue.ToPortID, nil
}

// ResetToPortID resets all changes to the "to_port_id" field.
func (m *WorkflowsEdgeMutation) ResetToPortID() {
	m.to_port_id = nil
}

// SetType sets the "type" field.
func (m *WorkflowsEdgeMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *WorkflowsEdgeMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *WorkflowsEdgeMutation) ResetType() {
	m._type = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *WorkflowsEdgeMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *WorkflowsEdgeMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *WorkflowsEdgeMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *WorkflowsEdgeMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *WorkflowsEdgeMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the WorkflowsEdge entity.
// If the WorkflowsEdge object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowsEdgeMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *WorkflowsEdgeMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (m *WorkflowsEdgeMutation) ClearWorkflow() {
	m.clearedworkflow = true
	m.clearedFields[workflowsedge.FieldWorkflowID] = struct{}{}
}

// WorkflowCleared reports if the "workflow" edge to the Workflow entity was cleared.
func (m *WorkflowsEdgeMutation) WorkflowCleared() bool {
	return m.clearedworkflow
}

// WorkflowIDs returns the "workflow" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// WorkflowID instead. It exists only for internal usage by the builders.
func (m *WorkflowsEdgeMutation) WorkflowIDs() (ids []uuid.UUID) {
	if id := m.workflow; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetWorkflow resets all changes to the "workflow" edge.
func (m *WorkflowsEdgeMutation) ResetWorkflow() {
	m.workflow = nil
	m.clearedworkflow = false
}

// Where appends a list predicates to the WorkflowsEdgeMutation builder.
func (m *WorkflowsEdgeMutation) Where(ps ...predicate.WorkflowsEdge) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the WorkflowsEdgeMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *WorkflowsEdgeMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.WorkflowsEdge, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *WorkflowsEdgeMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *WorkflowsEdgeMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (WorkflowsEdge).
func (m *WorkflowsEdgeMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *WorkflowsEdgeMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.workflow != nil {
		fields = append(fields, workflowsedge.FieldWorkflowID)
	}
	if m.from_node_id != nil {
		fields = append(fields, workflowsedge.FieldFromNodeID)
	}
	if m.to_node_id != nil {
		fields = append(fields, workflowsedge.FieldToNodeID)
	}
	if m.from_port_id != nil {
		fields = append(fields, workflowsedge.FieldFromPortID)
	}
	if m.to_port_id != nil {
		fields = append(fields, workflowsedge.FieldToPortID)
	}
	if m._type != nil {
		fields = append(fields, workflowsedge.FieldType)
	}
	if m.created_at != nil {
		fields = append(fields, workflowsedge.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, workflowsedge.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *WorkflowsEdgeMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case workflowsedge.FieldWorkflowID:
		return m.WorkflowID()
	case workflowsedge.FieldFromNodeID:
		return m.FromNodeID()
	case workflowsedge.FieldToNodeID:
		return m.ToNodeID()
	case workflowsedge.FieldFromPortID:
		return m.FromPortID()
	case workflowsedge.FieldToPortID:
		return m.ToPortID()
	case workflowsedge.FieldType:
		return m.GetType()
	case workflowsedge.FieldCreatedAt:
		return m.CreatedAt()
	case workflowsedge.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *WorkflowsEdgeMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case workflowsedge.FieldWorkflowID:
		return m.OldWorkflowID(ctx)
	case workflowsedge.FieldFromNodeID:
		return m.OldFromNodeID(ctx)
	case workflowsedge.FieldToNodeID:
		return m.OldToNodeID(ctx)
	case workflowsedge.FieldFromPortID:
		return m.OldFromPortID(ctx)
	case workflowsedge.FieldToPortID:
		return m.OldToPortID(ctx)
	case workflowsedge.FieldType:
		return m.OldType(ctx)
	case workflowsedge.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case workflowsedge.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown WorkflowsEdge field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowsEdgeMutation) SetField(name string, value ent.Value) error {
	switch name {
	case workflowsedge.FieldWorkflowID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkflowID(v)
		return nil
	case workflowsedge.FieldFromNodeID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFromNodeID(v)
		return nil
	case workflowsedge.FieldToNodeID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetToNodeID(v)
		return nil
	case workflowsedge.FieldFromPortID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFromPortID(v)
		return nil
	case workflowsedge.FieldToPortID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetToPortID(v)
		return nil
	case workflowsedge.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case workflowsedge.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case workflowsedge.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown WorkflowsEdge field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *WorkflowsEdgeMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *WorkflowsEdgeMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowsEdgeMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown WorkflowsEdge numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *WorkflowsEdgeMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *WorkflowsEdgeMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *WorkflowsEdgeMutation) ClearField(name string) error {
	return fmt.Errorf("unknown WorkflowsEdge nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *WorkflowsEdgeMutation) ResetField(name string) error {
	switch name {
	case workflowsedge.FieldWorkflowID:
		m.ResetWorkflowID()
		return nil
	case workflowsedge.FieldFromNodeID:
		m.ResetFromNodeID()
		return nil
	case workflowsedge.FieldToNodeID:
		m.ResetToNodeID()
		return nil
	case workflowsedge.FieldFromPortID:
		m.ResetFromPortID()
		return nil
	case workflowsedge.FieldToPortID:
		m.ResetToPortID()
		return nil
	case workflowsedge.FieldType:
		m.ResetType()
		return nil
	case workflowsedge.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case workflowsedge.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown WorkflowsEdge field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *WorkflowsEdgeMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.workflow != nil {
		edges = append(edges, workflowsedge.EdgeWorkflow)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *WorkflowsEdgeMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case workflowsedge.EdgeWorkflow:
		if id := m.workflow; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *WorkflowsEdgeMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *WorkflowsEdgeMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *WorkflowsEdgeMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedworkflow {
		edges = append(edges, workflowsedge.EdgeWorkflow)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *WorkflowsEdgeMutation) EdgeCleared(name string) bool {
	switch name {
	case workflowsedge.EdgeWorkflow:
		return m.clearedworkflow
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *WorkflowsEdgeMutation) ClearEdge(name string) error {
	switch name {
	case workflowsedge.EdgeWorkflow:
		m.ClearWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowsEdge unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *WorkflowsEdgeMutation) ResetEdge(name string) error {
	switch name {
	case workflowsedge.EdgeWorkflow:
		m.ResetWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowsEdge edge %s", name)
}
