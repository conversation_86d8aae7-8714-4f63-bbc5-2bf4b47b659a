// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"resflow/ent/predicate"
	"resflow/ent/workflow"
	"resflow/ent/workflowsedge"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowsEdgeUpdate is the builder for updating WorkflowsEdge entities.
type WorkflowsEdgeUpdate struct {
	config
	hooks    []Hook
	mutation *WorkflowsEdgeMutation
}

// Where appends a list predicates to the WorkflowsEdgeUpdate builder.
func (weu *WorkflowsEdgeUpdate) Where(ps ...predicate.WorkflowsEdge) *WorkflowsEdgeUpdate {
	weu.mutation.Where(ps...)
	return weu
}

// SetWorkflowID sets the "workflow_id" field.
func (weu *WorkflowsEdgeUpdate) SetWorkflowID(u uuid.UUID) *WorkflowsEdgeUpdate {
	weu.mutation.SetWorkflowID(u)
	return weu
}

// SetNillableWorkflowID sets the "workflow_id" field if the given value is not nil.
func (weu *WorkflowsEdgeUpdate) SetNillableWorkflowID(u *uuid.UUID) *WorkflowsEdgeUpdate {
	if u != nil {
		weu.SetWorkflowID(*u)
	}
	return weu
}

// SetFromNodeID sets the "from_node_id" field.
func (weu *WorkflowsEdgeUpdate) SetFromNodeID(u uuid.UUID) *WorkflowsEdgeUpdate {
	weu.mutation.SetFromNodeID(u)
	return weu
}

// SetNillableFromNodeID sets the "from_node_id" field if the given value is not nil.
func (weu *WorkflowsEdgeUpdate) SetNillableFromNodeID(u *uuid.UUID) *WorkflowsEdgeUpdate {
	if u != nil {
		weu.SetFromNodeID(*u)
	}
	return weu
}

// SetToNodeID sets the "to_node_id" field.
func (weu *WorkflowsEdgeUpdate) SetToNodeID(u uuid.UUID) *WorkflowsEdgeUpdate {
	weu.mutation.SetToNodeID(u)
	return weu
}

// SetNillableToNodeID sets the "to_node_id" field if the given value is not nil.
func (weu *WorkflowsEdgeUpdate) SetNillableToNodeID(u *uuid.UUID) *WorkflowsEdgeUpdate {
	if u != nil {
		weu.SetToNodeID(*u)
	}
	return weu
}

// SetFromPortID sets the "from_port_id" field.
func (weu *WorkflowsEdgeUpdate) SetFromPortID(s string) *WorkflowsEdgeUpdate {
	weu.mutation.SetFromPortID(s)
	return weu
}

// SetNillableFromPortID sets the "from_port_id" field if the given value is not nil.
func (weu *WorkflowsEdgeUpdate) SetNillableFromPortID(s *string) *WorkflowsEdgeUpdate {
	if s != nil {
		weu.SetFromPortID(*s)
	}
	return weu
}

// SetToPortID sets the "to_port_id" field.
func (weu *WorkflowsEdgeUpdate) SetToPortID(s string) *WorkflowsEdgeUpdate {
	weu.mutation.SetToPortID(s)
	return weu
}

// SetNillableToPortID sets the "to_port_id" field if the given value is not nil.
func (weu *WorkflowsEdgeUpdate) SetNillableToPortID(s *string) *WorkflowsEdgeUpdate {
	if s != nil {
		weu.SetToPortID(*s)
	}
	return weu
}

// SetType sets the "type" field.
func (weu *WorkflowsEdgeUpdate) SetType(s string) *WorkflowsEdgeUpdate {
	weu.mutation.SetType(s)
	return weu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (weu *WorkflowsEdgeUpdate) SetNillableType(s *string) *WorkflowsEdgeUpdate {
	if s != nil {
		weu.SetType(*s)
	}
	return weu
}

// SetUpdatedAt sets the "updated_at" field.
func (weu *WorkflowsEdgeUpdate) SetUpdatedAt(t time.Time) *WorkflowsEdgeUpdate {
	weu.mutation.SetUpdatedAt(t)
	return weu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (weu *WorkflowsEdgeUpdate) SetNillableUpdatedAt(t *time.Time) *WorkflowsEdgeUpdate {
	if t != nil {
		weu.SetUpdatedAt(*t)
	}
	return weu
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (weu *WorkflowsEdgeUpdate) SetWorkflow(w *Workflow) *WorkflowsEdgeUpdate {
	return weu.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowsEdgeMutation object of the builder.
func (weu *WorkflowsEdgeUpdate) Mutation() *WorkflowsEdgeMutation {
	return weu.mutation
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (weu *WorkflowsEdgeUpdate) ClearWorkflow() *WorkflowsEdgeUpdate {
	weu.mutation.ClearWorkflow()
	return weu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (weu *WorkflowsEdgeUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, weu.sqlSave, weu.mutation, weu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (weu *WorkflowsEdgeUpdate) SaveX(ctx context.Context) int {
	affected, err := weu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (weu *WorkflowsEdgeUpdate) Exec(ctx context.Context) error {
	_, err := weu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (weu *WorkflowsEdgeUpdate) ExecX(ctx context.Context) {
	if err := weu.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (weu *WorkflowsEdgeUpdate) check() error {
	if weu.mutation.WorkflowCleared() && len(weu.mutation.WorkflowIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "WorkflowsEdge.workflow"`)
	}
	return nil
}

func (weu *WorkflowsEdgeUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := weu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflowsedge.Table, workflowsedge.Columns, sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID))
	if ps := weu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := weu.mutation.FromNodeID(); ok {
		_spec.SetField(workflowsedge.FieldFromNodeID, field.TypeUUID, value)
	}
	if value, ok := weu.mutation.ToNodeID(); ok {
		_spec.SetField(workflowsedge.FieldToNodeID, field.TypeUUID, value)
	}
	if value, ok := weu.mutation.FromPortID(); ok {
		_spec.SetField(workflowsedge.FieldFromPortID, field.TypeString, value)
	}
	if value, ok := weu.mutation.ToPortID(); ok {
		_spec.SetField(workflowsedge.FieldToPortID, field.TypeString, value)
	}
	if value, ok := weu.mutation.GetType(); ok {
		_spec.SetField(workflowsedge.FieldType, field.TypeString, value)
	}
	if value, ok := weu.mutation.UpdatedAt(); ok {
		_spec.SetField(workflowsedge.FieldUpdatedAt, field.TypeTime, value)
	}
	if weu.mutation.WorkflowCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowsedge.WorkflowTable,
			Columns: []string{workflowsedge.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := weu.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowsedge.WorkflowTable,
			Columns: []string{workflowsedge.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, weu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflowsedge.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	weu.mutation.done = true
	return n, nil
}

// WorkflowsEdgeUpdateOne is the builder for updating a single WorkflowsEdge entity.
type WorkflowsEdgeUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WorkflowsEdgeMutation
}

// SetWorkflowID sets the "workflow_id" field.
func (weuo *WorkflowsEdgeUpdateOne) SetWorkflowID(u uuid.UUID) *WorkflowsEdgeUpdateOne {
	weuo.mutation.SetWorkflowID(u)
	return weuo
}

// SetNillableWorkflowID sets the "workflow_id" field if the given value is not nil.
func (weuo *WorkflowsEdgeUpdateOne) SetNillableWorkflowID(u *uuid.UUID) *WorkflowsEdgeUpdateOne {
	if u != nil {
		weuo.SetWorkflowID(*u)
	}
	return weuo
}

// SetFromNodeID sets the "from_node_id" field.
func (weuo *WorkflowsEdgeUpdateOne) SetFromNodeID(u uuid.UUID) *WorkflowsEdgeUpdateOne {
	weuo.mutation.SetFromNodeID(u)
	return weuo
}

// SetNillableFromNodeID sets the "from_node_id" field if the given value is not nil.
func (weuo *WorkflowsEdgeUpdateOne) SetNillableFromNodeID(u *uuid.UUID) *WorkflowsEdgeUpdateOne {
	if u != nil {
		weuo.SetFromNodeID(*u)
	}
	return weuo
}

// SetToNodeID sets the "to_node_id" field.
func (weuo *WorkflowsEdgeUpdateOne) SetToNodeID(u uuid.UUID) *WorkflowsEdgeUpdateOne {
	weuo.mutation.SetToNodeID(u)
	return weuo
}

// SetNillableToNodeID sets the "to_node_id" field if the given value is not nil.
func (weuo *WorkflowsEdgeUpdateOne) SetNillableToNodeID(u *uuid.UUID) *WorkflowsEdgeUpdateOne {
	if u != nil {
		weuo.SetToNodeID(*u)
	}
	return weuo
}

// SetFromPortID sets the "from_port_id" field.
func (weuo *WorkflowsEdgeUpdateOne) SetFromPortID(s string) *WorkflowsEdgeUpdateOne {
	weuo.mutation.SetFromPortID(s)
	return weuo
}

// SetNillableFromPortID sets the "from_port_id" field if the given value is not nil.
func (weuo *WorkflowsEdgeUpdateOne) SetNillableFromPortID(s *string) *WorkflowsEdgeUpdateOne {
	if s != nil {
		weuo.SetFromPortID(*s)
	}
	return weuo
}

// SetToPortID sets the "to_port_id" field.
func (weuo *WorkflowsEdgeUpdateOne) SetToPortID(s string) *WorkflowsEdgeUpdateOne {
	weuo.mutation.SetToPortID(s)
	return weuo
}

// SetNillableToPortID sets the "to_port_id" field if the given value is not nil.
func (weuo *WorkflowsEdgeUpdateOne) SetNillableToPortID(s *string) *WorkflowsEdgeUpdateOne {
	if s != nil {
		weuo.SetToPortID(*s)
	}
	return weuo
}

// SetType sets the "type" field.
func (weuo *WorkflowsEdgeUpdateOne) SetType(s string) *WorkflowsEdgeUpdateOne {
	weuo.mutation.SetType(s)
	return weuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (weuo *WorkflowsEdgeUpdateOne) SetNillableType(s *string) *WorkflowsEdgeUpdateOne {
	if s != nil {
		weuo.SetType(*s)
	}
	return weuo
}

// SetUpdatedAt sets the "updated_at" field.
func (weuo *WorkflowsEdgeUpdateOne) SetUpdatedAt(t time.Time) *WorkflowsEdgeUpdateOne {
	weuo.mutation.SetUpdatedAt(t)
	return weuo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (weuo *WorkflowsEdgeUpdateOne) SetNillableUpdatedAt(t *time.Time) *WorkflowsEdgeUpdateOne {
	if t != nil {
		weuo.SetUpdatedAt(*t)
	}
	return weuo
}

// SetWorkflow sets the "workflow" edge to the Workflow entity.
func (weuo *WorkflowsEdgeUpdateOne) SetWorkflow(w *Workflow) *WorkflowsEdgeUpdateOne {
	return weuo.SetWorkflowID(w.ID)
}

// Mutation returns the WorkflowsEdgeMutation object of the builder.
func (weuo *WorkflowsEdgeUpdateOne) Mutation() *WorkflowsEdgeMutation {
	return weuo.mutation
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (weuo *WorkflowsEdgeUpdateOne) ClearWorkflow() *WorkflowsEdgeUpdateOne {
	weuo.mutation.ClearWorkflow()
	return weuo
}

// Where appends a list predicates to the WorkflowsEdgeUpdate builder.
func (weuo *WorkflowsEdgeUpdateOne) Where(ps ...predicate.WorkflowsEdge) *WorkflowsEdgeUpdateOne {
	weuo.mutation.Where(ps...)
	return weuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (weuo *WorkflowsEdgeUpdateOne) Select(field string, fields ...string) *WorkflowsEdgeUpdateOne {
	weuo.fields = append([]string{field}, fields...)
	return weuo
}

// Save executes the query and returns the updated WorkflowsEdge entity.
func (weuo *WorkflowsEdgeUpdateOne) Save(ctx context.Context) (*WorkflowsEdge, error) {
	return withHooks(ctx, weuo.sqlSave, weuo.mutation, weuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (weuo *WorkflowsEdgeUpdateOne) SaveX(ctx context.Context) *WorkflowsEdge {
	node, err := weuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (weuo *WorkflowsEdgeUpdateOne) Exec(ctx context.Context) error {
	_, err := weuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (weuo *WorkflowsEdgeUpdateOne) ExecX(ctx context.Context) {
	if err := weuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (weuo *WorkflowsEdgeUpdateOne) check() error {
	if weuo.mutation.WorkflowCleared() && len(weuo.mutation.WorkflowIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "WorkflowsEdge.workflow"`)
	}
	return nil
}

func (weuo *WorkflowsEdgeUpdateOne) sqlSave(ctx context.Context) (_node *WorkflowsEdge, err error) {
	if err := weuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(workflowsedge.Table, workflowsedge.Columns, sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID))
	id, ok := weuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "WorkflowsEdge.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := weuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflowsedge.FieldID)
		for _, f := range fields {
			if !workflowsedge.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != workflowsedge.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := weuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := weuo.mutation.FromNodeID(); ok {
		_spec.SetField(workflowsedge.FieldFromNodeID, field.TypeUUID, value)
	}
	if value, ok := weuo.mutation.ToNodeID(); ok {
		_spec.SetField(workflowsedge.FieldToNodeID, field.TypeUUID, value)
	}
	if value, ok := weuo.mutation.FromPortID(); ok {
		_spec.SetField(workflowsedge.FieldFromPortID, field.TypeString, value)
	}
	if value, ok := weuo.mutation.ToPortID(); ok {
		_spec.SetField(workflowsedge.FieldToPortID, field.TypeString, value)
	}
	if value, ok := weuo.mutation.GetType(); ok {
		_spec.SetField(workflowsedge.FieldType, field.TypeString, value)
	}
	if value, ok := weuo.mutation.UpdatedAt(); ok {
		_spec.SetField(workflowsedge.FieldUpdatedAt, field.TypeTime, value)
	}
	if weuo.mutation.WorkflowCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowsedge.WorkflowTable,
			Columns: []string{workflowsedge.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := weuo.mutation.WorkflowIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflowsedge.WorkflowTable,
			Columns: []string{workflowsedge.WorkflowColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &WorkflowsEdge{config: weuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, weuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflowsedge.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	weuo.mutation.done = true
	return _node, nil
}
