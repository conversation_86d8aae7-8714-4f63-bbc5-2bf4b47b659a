// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// NodeDefinitionsColumns holds the columns for the "node_definitions" table.
	NodeDefinitionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "plugin_name", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "author", Type: field.TypeString},
		{Name: "description", Type: field.TypeString},
		{Name: "icon", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "version", Type: field.TypeString},
		{Name: "category", Type: field.TypeString},
		{Name: "input_params", Type: field.TypeJSON},
		{Name: "output_params", Type: field.TypeJSON},
		{Name: "input_ports", Type: field.TypeJSON},
		{Name: "output_ports", Type: field.TypeJSON},
		{Name: "exception", Type: field.TypeBool},
		{Name: "path", Type: field.TypeString},
		{Name: "builtin", Type: field.TypeBool},
		{Name: "enabled", Type: field.TypeBool},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// NodeDefinitionsTable holds the schema information for the "node_definitions" table.
	NodeDefinitionsTable = &schema.Table{
		Name:       "node_definitions",
		Columns:    NodeDefinitionsColumns,
		PrimaryKey: []*schema.Column{NodeDefinitionsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "nodedefinition_type_version_plugin_name",
				Unique:  true,
				Columns: []*schema.Column{NodeDefinitionsColumns[6], NodeDefinitionsColumns[7], NodeDefinitionsColumns[1]},
			},
		},
	}
	// PluginsColumns holds the columns for the "plugins" table.
	PluginsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "name", Type: field.TypeString},
		{Name: "version", Type: field.TypeString},
		{Name: "author", Type: field.TypeString},
		{Name: "display_name", Type: field.TypeString},
		{Name: "description", Type: field.TypeString},
		{Name: "icon", Type: field.TypeString},
		{Name: "path", Type: field.TypeString},
		{Name: "builtin", Type: field.TypeBool},
		{Name: "enabled", Type: field.TypeBool},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// PluginsTable holds the schema information for the "plugins" table.
	PluginsTable = &schema.Table{
		Name:       "plugins",
		Columns:    PluginsColumns,
		PrimaryKey: []*schema.Column{PluginsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "plugin_name_version",
				Unique:  true,
				Columns: []*schema.Column{PluginsColumns[1], PluginsColumns[2]},
			},
		},
	}
	// UsersColumns holds the columns for the "users" table.
	UsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "username", Type: field.TypeString},
		{Name: "password", Type: field.TypeString},
		{Name: "nickname", Type: field.TypeString, Nullable: true},
		{Name: "status", Type: field.TypeInt},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
	}
	// UsersTable holds the schema information for the "users" table.
	UsersTable = &schema.Table{
		Name:       "users",
		Columns:    UsersColumns,
		PrimaryKey: []*schema.Column{UsersColumns[0]},
	}
	// WorkflowsColumns holds the columns for the "workflows" table.
	WorkflowsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "name", Type: field.TypeString},
		{Name: "icon_type", Type: field.TypeString},
		{Name: "icon_data", Type: field.TypeString},
		{Name: "description", Type: field.TypeString},
		{Name: "status", Type: field.TypeInt},
		{Name: "viewport", Type: field.TypeJSON},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeUUID, Nullable: true},
	}
	// WorkflowsTable holds the schema information for the "workflows" table.
	WorkflowsTable = &schema.Table{
		Name:       "workflows",
		Columns:    WorkflowsColumns,
		PrimaryKey: []*schema.Column{WorkflowsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "workflows_users_workflows",
				Columns:    []*schema.Column{WorkflowsColumns[9]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WorkflowNodesColumns holds the columns for the "workflow_nodes" table.
	WorkflowNodesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "name", Type: field.TypeString},
		{Name: "description", Type: field.TypeString},
		{Name: "icon_type", Type: field.TypeString},
		{Name: "icon_data", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "version", Type: field.TypeString},
		{Name: "plugin_name", Type: field.TypeString},
		{Name: "plugin_version", Type: field.TypeString},
		{Name: "input_params", Type: field.TypeJSON},
		{Name: "output_params", Type: field.TypeJSON},
		{Name: "input_ports", Type: field.TypeJSON},
		{Name: "output_ports", Type: field.TypeJSON},
		{Name: "position", Type: field.TypeJSON},
		{Name: "config_data", Type: field.TypeJSON, Nullable: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "workflow_id", Type: field.TypeUUID},
	}
	// WorkflowNodesTable holds the schema information for the "workflow_nodes" table.
	WorkflowNodesTable = &schema.Table{
		Name:       "workflow_nodes",
		Columns:    WorkflowNodesColumns,
		PrimaryKey: []*schema.Column{WorkflowNodesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "workflow_nodes_workflows_nodes",
				Columns:    []*schema.Column{WorkflowNodesColumns[17]},
				RefColumns: []*schema.Column{WorkflowsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// WorkflowsEdgesColumns holds the columns for the "workflows_edges" table.
	WorkflowsEdgesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID},
		{Name: "from_node_id", Type: field.TypeUUID},
		{Name: "to_node_id", Type: field.TypeUUID},
		{Name: "from_port_id", Type: field.TypeString},
		{Name: "to_port_id", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "workflow_id", Type: field.TypeUUID},
	}
	// WorkflowsEdgesTable holds the schema information for the "workflows_edges" table.
	WorkflowsEdgesTable = &schema.Table{
		Name:       "workflows_edges",
		Columns:    WorkflowsEdgesColumns,
		PrimaryKey: []*schema.Column{WorkflowsEdgesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "workflows_edges_workflows_workflow_edges",
				Columns:    []*schema.Column{WorkflowsEdgesColumns[8]},
				RefColumns: []*schema.Column{WorkflowsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		NodeDefinitionsTable,
		PluginsTable,
		UsersTable,
		WorkflowsTable,
		WorkflowNodesTable,
		WorkflowsEdgesTable,
	}
)

func init() {
	WorkflowsTable.ForeignKeys[0].RefTable = UsersTable
	WorkflowNodesTable.ForeignKeys[0].RefTable = WorkflowsTable
	WorkflowsEdgesTable.ForeignKeys[0].RefTable = WorkflowsTable
}
