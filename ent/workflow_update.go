// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"resflow/ent/predicate"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"github.com/google/uuid"
)

// WorkflowUpdate is the builder for updating Workflow entities.
type WorkflowUpdate struct {
	config
	hooks    []Hook
	mutation *WorkflowMutation
}

// Where appends a list predicates to the WorkflowUpdate builder.
func (wu *WorkflowUpdate) Where(ps ...predicate.Workflow) *WorkflowUpdate {
	wu.mutation.Where(ps...)
	return wu
}

// SetUserID sets the "user_id" field.
func (wu *WorkflowUpdate) SetUserID(u uuid.UUID) *WorkflowUpdate {
	wu.mutation.SetUserID(u)
	return wu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableUserID(u *uuid.UUID) *WorkflowUpdate {
	if u != nil {
		wu.SetUserID(*u)
	}
	return wu
}

// ClearUserID clears the value of the "user_id" field.
func (wu *WorkflowUpdate) ClearUserID() *WorkflowUpdate {
	wu.mutation.ClearUserID()
	return wu
}

// SetName sets the "name" field.
func (wu *WorkflowUpdate) SetName(s string) *WorkflowUpdate {
	wu.mutation.SetName(s)
	return wu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableName(s *string) *WorkflowUpdate {
	if s != nil {
		wu.SetName(*s)
	}
	return wu
}

// SetIconType sets the "icon_type" field.
func (wu *WorkflowUpdate) SetIconType(s string) *WorkflowUpdate {
	wu.mutation.SetIconType(s)
	return wu
}

// SetNillableIconType sets the "icon_type" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableIconType(s *string) *WorkflowUpdate {
	if s != nil {
		wu.SetIconType(*s)
	}
	return wu
}

// SetIconData sets the "icon_data" field.
func (wu *WorkflowUpdate) SetIconData(s string) *WorkflowUpdate {
	wu.mutation.SetIconData(s)
	return wu
}

// SetNillableIconData sets the "icon_data" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableIconData(s *string) *WorkflowUpdate {
	if s != nil {
		wu.SetIconData(*s)
	}
	return wu
}

// SetDescription sets the "description" field.
func (wu *WorkflowUpdate) SetDescription(s string) *WorkflowUpdate {
	wu.mutation.SetDescription(s)
	return wu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableDescription(s *string) *WorkflowUpdate {
	if s != nil {
		wu.SetDescription(*s)
	}
	return wu
}

// SetStatus sets the "status" field.
func (wu *WorkflowUpdate) SetStatus(i int) *WorkflowUpdate {
	wu.mutation.ResetStatus()
	wu.mutation.SetStatus(i)
	return wu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableStatus(i *int) *WorkflowUpdate {
	if i != nil {
		wu.SetStatus(*i)
	}
	return wu
}

// AddStatus adds i to the "status" field.
func (wu *WorkflowUpdate) AddStatus(i int) *WorkflowUpdate {
	wu.mutation.AddStatus(i)
	return wu
}

// SetViewport sets the "viewport" field.
func (wu *WorkflowUpdate) SetViewport(jm json.RawMessage) *WorkflowUpdate {
	wu.mutation.SetViewport(jm)
	return wu
}

// AppendViewport appends jm to the "viewport" field.
func (wu *WorkflowUpdate) AppendViewport(jm json.RawMessage) *WorkflowUpdate {
	wu.mutation.AppendViewport(jm)
	return wu
}

// SetUpdatedAt sets the "updated_at" field.
func (wu *WorkflowUpdate) SetUpdatedAt(t time.Time) *WorkflowUpdate {
	wu.mutation.SetUpdatedAt(t)
	return wu
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wu *WorkflowUpdate) SetNillableUpdatedAt(t *time.Time) *WorkflowUpdate {
	if t != nil {
		wu.SetUpdatedAt(*t)
	}
	return wu
}

// SetUser sets the "user" edge to the User entity.
func (wu *WorkflowUpdate) SetUser(u *User) *WorkflowUpdate {
	return wu.SetUserID(u.ID)
}

// AddNodeIDs adds the "nodes" edge to the WorkflowNode entity by IDs.
func (wu *WorkflowUpdate) AddNodeIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.AddNodeIDs(ids...)
	return wu
}

// AddNodes adds the "nodes" edges to the WorkflowNode entity.
func (wu *WorkflowUpdate) AddNodes(w ...*WorkflowNode) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.AddNodeIDs(ids...)
}

// AddWorkflowEdgeIDs adds the "workflow_edges" edge to the WorkflowsEdge entity by IDs.
func (wu *WorkflowUpdate) AddWorkflowEdgeIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.AddWorkflowEdgeIDs(ids...)
	return wu
}

// AddWorkflowEdges adds the "workflow_edges" edges to the WorkflowsEdge entity.
func (wu *WorkflowUpdate) AddWorkflowEdges(w ...*WorkflowsEdge) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.AddWorkflowEdgeIDs(ids...)
}

// Mutation returns the WorkflowMutation object of the builder.
func (wu *WorkflowUpdate) Mutation() *WorkflowMutation {
	return wu.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (wu *WorkflowUpdate) ClearUser() *WorkflowUpdate {
	wu.mutation.ClearUser()
	return wu
}

// ClearNodes clears all "nodes" edges to the WorkflowNode entity.
func (wu *WorkflowUpdate) ClearNodes() *WorkflowUpdate {
	wu.mutation.ClearNodes()
	return wu
}

// RemoveNodeIDs removes the "nodes" edge to WorkflowNode entities by IDs.
func (wu *WorkflowUpdate) RemoveNodeIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.RemoveNodeIDs(ids...)
	return wu
}

// RemoveNodes removes "nodes" edges to WorkflowNode entities.
func (wu *WorkflowUpdate) RemoveNodes(w ...*WorkflowNode) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.RemoveNodeIDs(ids...)
}

// ClearWorkflowEdges clears all "workflow_edges" edges to the WorkflowsEdge entity.
func (wu *WorkflowUpdate) ClearWorkflowEdges() *WorkflowUpdate {
	wu.mutation.ClearWorkflowEdges()
	return wu
}

// RemoveWorkflowEdgeIDs removes the "workflow_edges" edge to WorkflowsEdge entities by IDs.
func (wu *WorkflowUpdate) RemoveWorkflowEdgeIDs(ids ...uuid.UUID) *WorkflowUpdate {
	wu.mutation.RemoveWorkflowEdgeIDs(ids...)
	return wu
}

// RemoveWorkflowEdges removes "workflow_edges" edges to WorkflowsEdge entities.
func (wu *WorkflowUpdate) RemoveWorkflowEdges(w ...*WorkflowsEdge) *WorkflowUpdate {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wu.RemoveWorkflowEdgeIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wu *WorkflowUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, wu.sqlSave, wu.mutation, wu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wu *WorkflowUpdate) SaveX(ctx context.Context) int {
	affected, err := wu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wu *WorkflowUpdate) Exec(ctx context.Context) error {
	_, err := wu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wu *WorkflowUpdate) ExecX(ctx context.Context) {
	if err := wu.Exec(ctx); err != nil {
		panic(err)
	}
}

func (wu *WorkflowUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(workflow.Table, workflow.Columns, sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID))
	if ps := wu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wu.mutation.Name(); ok {
		_spec.SetField(workflow.FieldName, field.TypeString, value)
	}
	if value, ok := wu.mutation.IconType(); ok {
		_spec.SetField(workflow.FieldIconType, field.TypeString, value)
	}
	if value, ok := wu.mutation.IconData(); ok {
		_spec.SetField(workflow.FieldIconData, field.TypeString, value)
	}
	if value, ok := wu.mutation.Description(); ok {
		_spec.SetField(workflow.FieldDescription, field.TypeString, value)
	}
	if value, ok := wu.mutation.Status(); ok {
		_spec.SetField(workflow.FieldStatus, field.TypeInt, value)
	}
	if value, ok := wu.mutation.AddedStatus(); ok {
		_spec.AddField(workflow.FieldStatus, field.TypeInt, value)
	}
	if value, ok := wu.mutation.Viewport(); ok {
		_spec.SetField(workflow.FieldViewport, field.TypeJSON, value)
	}
	if value, ok := wu.mutation.AppendedViewport(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflow.FieldViewport, value)
		})
	}
	if value, ok := wu.mutation.UpdatedAt(); ok {
		_spec.SetField(workflow.FieldUpdatedAt, field.TypeTime, value)
	}
	if wu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wu.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.RemovedNodesIDs(); len(nodes) > 0 && !wu.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.NodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wu.mutation.WorkflowEdgesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.WorkflowEdgesTable,
			Columns: []string{workflow.WorkflowEdgesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.RemovedWorkflowEdgesIDs(); len(nodes) > 0 && !wu.mutation.WorkflowEdgesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.WorkflowEdgesTable,
			Columns: []string{workflow.WorkflowEdgesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.WorkflowEdgesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.WorkflowEdgesTable,
			Columns: []string{workflow.WorkflowEdgesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, wu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflow.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wu.mutation.done = true
	return n, nil
}

// WorkflowUpdateOne is the builder for updating a single Workflow entity.
type WorkflowUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WorkflowMutation
}

// SetUserID sets the "user_id" field.
func (wuo *WorkflowUpdateOne) SetUserID(u uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.SetUserID(u)
	return wuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableUserID(u *uuid.UUID) *WorkflowUpdateOne {
	if u != nil {
		wuo.SetUserID(*u)
	}
	return wuo
}

// ClearUserID clears the value of the "user_id" field.
func (wuo *WorkflowUpdateOne) ClearUserID() *WorkflowUpdateOne {
	wuo.mutation.ClearUserID()
	return wuo
}

// SetName sets the "name" field.
func (wuo *WorkflowUpdateOne) SetName(s string) *WorkflowUpdateOne {
	wuo.mutation.SetName(s)
	return wuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableName(s *string) *WorkflowUpdateOne {
	if s != nil {
		wuo.SetName(*s)
	}
	return wuo
}

// SetIconType sets the "icon_type" field.
func (wuo *WorkflowUpdateOne) SetIconType(s string) *WorkflowUpdateOne {
	wuo.mutation.SetIconType(s)
	return wuo
}

// SetNillableIconType sets the "icon_type" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableIconType(s *string) *WorkflowUpdateOne {
	if s != nil {
		wuo.SetIconType(*s)
	}
	return wuo
}

// SetIconData sets the "icon_data" field.
func (wuo *WorkflowUpdateOne) SetIconData(s string) *WorkflowUpdateOne {
	wuo.mutation.SetIconData(s)
	return wuo
}

// SetNillableIconData sets the "icon_data" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableIconData(s *string) *WorkflowUpdateOne {
	if s != nil {
		wuo.SetIconData(*s)
	}
	return wuo
}

// SetDescription sets the "description" field.
func (wuo *WorkflowUpdateOne) SetDescription(s string) *WorkflowUpdateOne {
	wuo.mutation.SetDescription(s)
	return wuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableDescription(s *string) *WorkflowUpdateOne {
	if s != nil {
		wuo.SetDescription(*s)
	}
	return wuo
}

// SetStatus sets the "status" field.
func (wuo *WorkflowUpdateOne) SetStatus(i int) *WorkflowUpdateOne {
	wuo.mutation.ResetStatus()
	wuo.mutation.SetStatus(i)
	return wuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableStatus(i *int) *WorkflowUpdateOne {
	if i != nil {
		wuo.SetStatus(*i)
	}
	return wuo
}

// AddStatus adds i to the "status" field.
func (wuo *WorkflowUpdateOne) AddStatus(i int) *WorkflowUpdateOne {
	wuo.mutation.AddStatus(i)
	return wuo
}

// SetViewport sets the "viewport" field.
func (wuo *WorkflowUpdateOne) SetViewport(jm json.RawMessage) *WorkflowUpdateOne {
	wuo.mutation.SetViewport(jm)
	return wuo
}

// AppendViewport appends jm to the "viewport" field.
func (wuo *WorkflowUpdateOne) AppendViewport(jm json.RawMessage) *WorkflowUpdateOne {
	wuo.mutation.AppendViewport(jm)
	return wuo
}

// SetUpdatedAt sets the "updated_at" field.
func (wuo *WorkflowUpdateOne) SetUpdatedAt(t time.Time) *WorkflowUpdateOne {
	wuo.mutation.SetUpdatedAt(t)
	return wuo
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wuo *WorkflowUpdateOne) SetNillableUpdatedAt(t *time.Time) *WorkflowUpdateOne {
	if t != nil {
		wuo.SetUpdatedAt(*t)
	}
	return wuo
}

// SetUser sets the "user" edge to the User entity.
func (wuo *WorkflowUpdateOne) SetUser(u *User) *WorkflowUpdateOne {
	return wuo.SetUserID(u.ID)
}

// AddNodeIDs adds the "nodes" edge to the WorkflowNode entity by IDs.
func (wuo *WorkflowUpdateOne) AddNodeIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.AddNodeIDs(ids...)
	return wuo
}

// AddNodes adds the "nodes" edges to the WorkflowNode entity.
func (wuo *WorkflowUpdateOne) AddNodes(w ...*WorkflowNode) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.AddNodeIDs(ids...)
}

// AddWorkflowEdgeIDs adds the "workflow_edges" edge to the WorkflowsEdge entity by IDs.
func (wuo *WorkflowUpdateOne) AddWorkflowEdgeIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.AddWorkflowEdgeIDs(ids...)
	return wuo
}

// AddWorkflowEdges adds the "workflow_edges" edges to the WorkflowsEdge entity.
func (wuo *WorkflowUpdateOne) AddWorkflowEdges(w ...*WorkflowsEdge) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.AddWorkflowEdgeIDs(ids...)
}

// Mutation returns the WorkflowMutation object of the builder.
func (wuo *WorkflowUpdateOne) Mutation() *WorkflowMutation {
	return wuo.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (wuo *WorkflowUpdateOne) ClearUser() *WorkflowUpdateOne {
	wuo.mutation.ClearUser()
	return wuo
}

// ClearNodes clears all "nodes" edges to the WorkflowNode entity.
func (wuo *WorkflowUpdateOne) ClearNodes() *WorkflowUpdateOne {
	wuo.mutation.ClearNodes()
	return wuo
}

// RemoveNodeIDs removes the "nodes" edge to WorkflowNode entities by IDs.
func (wuo *WorkflowUpdateOne) RemoveNodeIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.RemoveNodeIDs(ids...)
	return wuo
}

// RemoveNodes removes "nodes" edges to WorkflowNode entities.
func (wuo *WorkflowUpdateOne) RemoveNodes(w ...*WorkflowNode) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.RemoveNodeIDs(ids...)
}

// ClearWorkflowEdges clears all "workflow_edges" edges to the WorkflowsEdge entity.
func (wuo *WorkflowUpdateOne) ClearWorkflowEdges() *WorkflowUpdateOne {
	wuo.mutation.ClearWorkflowEdges()
	return wuo
}

// RemoveWorkflowEdgeIDs removes the "workflow_edges" edge to WorkflowsEdge entities by IDs.
func (wuo *WorkflowUpdateOne) RemoveWorkflowEdgeIDs(ids ...uuid.UUID) *WorkflowUpdateOne {
	wuo.mutation.RemoveWorkflowEdgeIDs(ids...)
	return wuo
}

// RemoveWorkflowEdges removes "workflow_edges" edges to WorkflowsEdge entities.
func (wuo *WorkflowUpdateOne) RemoveWorkflowEdges(w ...*WorkflowsEdge) *WorkflowUpdateOne {
	ids := make([]uuid.UUID, len(w))
	for i := range w {
		ids[i] = w[i].ID
	}
	return wuo.RemoveWorkflowEdgeIDs(ids...)
}

// Where appends a list predicates to the WorkflowUpdate builder.
func (wuo *WorkflowUpdateOne) Where(ps ...predicate.Workflow) *WorkflowUpdateOne {
	wuo.mutation.Where(ps...)
	return wuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wuo *WorkflowUpdateOne) Select(field string, fields ...string) *WorkflowUpdateOne {
	wuo.fields = append([]string{field}, fields...)
	return wuo
}

// Save executes the query and returns the updated Workflow entity.
func (wuo *WorkflowUpdateOne) Save(ctx context.Context) (*Workflow, error) {
	return withHooks(ctx, wuo.sqlSave, wuo.mutation, wuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wuo *WorkflowUpdateOne) SaveX(ctx context.Context) *Workflow {
	node, err := wuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wuo *WorkflowUpdateOne) Exec(ctx context.Context) error {
	_, err := wuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wuo *WorkflowUpdateOne) ExecX(ctx context.Context) {
	if err := wuo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (wuo *WorkflowUpdateOne) sqlSave(ctx context.Context) (_node *Workflow, err error) {
	_spec := sqlgraph.NewUpdateSpec(workflow.Table, workflow.Columns, sqlgraph.NewFieldSpec(workflow.FieldID, field.TypeUUID))
	id, ok := wuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Workflow.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, workflow.FieldID)
		for _, f := range fields {
			if !workflow.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != workflow.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wuo.mutation.Name(); ok {
		_spec.SetField(workflow.FieldName, field.TypeString, value)
	}
	if value, ok := wuo.mutation.IconType(); ok {
		_spec.SetField(workflow.FieldIconType, field.TypeString, value)
	}
	if value, ok := wuo.mutation.IconData(); ok {
		_spec.SetField(workflow.FieldIconData, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Description(); ok {
		_spec.SetField(workflow.FieldDescription, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Status(); ok {
		_spec.SetField(workflow.FieldStatus, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.AddedStatus(); ok {
		_spec.AddField(workflow.FieldStatus, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.Viewport(); ok {
		_spec.SetField(workflow.FieldViewport, field.TypeJSON, value)
	}
	if value, ok := wuo.mutation.AppendedViewport(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, workflow.FieldViewport, value)
		})
	}
	if value, ok := wuo.mutation.UpdatedAt(); ok {
		_spec.SetField(workflow.FieldUpdatedAt, field.TypeTime, value)
	}
	if wuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   workflow.UserTable,
			Columns: []string{workflow.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wuo.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.RemovedNodesIDs(); len(nodes) > 0 && !wuo.mutation.NodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.NodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.NodesTable,
			Columns: []string{workflow.NodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflownode.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wuo.mutation.WorkflowEdgesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.WorkflowEdgesTable,
			Columns: []string{workflow.WorkflowEdgesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.RemovedWorkflowEdgesIDs(); len(nodes) > 0 && !wuo.mutation.WorkflowEdgesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.WorkflowEdgesTable,
			Columns: []string{workflow.WorkflowEdgesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.WorkflowEdgesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   workflow.WorkflowEdgesTable,
			Columns: []string{workflow.WorkflowEdgesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(workflowsedge.FieldID, field.TypeUUID),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Workflow{config: wuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{workflow.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wuo.mutation.done = true
	return _node, nil
}
