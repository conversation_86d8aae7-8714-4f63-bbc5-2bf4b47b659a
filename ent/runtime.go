// Code generated by ent, DO NOT EDIT.

package ent

import (
	"resflow/ent/nodedefinition"
	"resflow/ent/plugin"
	"resflow/ent/schema"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflownode"
	"resflow/ent/workflowsedge"
	"time"

	"github.com/google/uuid"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	nodedefinitionFields := schema.NodeDefinition{}.Fields()
	_ = nodedefinitionFields
	// nodedefinitionDescCreatedAt is the schema descriptor for created_at field.
	nodedefinitionDescCreatedAt := nodedefinitionFields[17].Descriptor()
	// nodedefinition.DefaultCreatedAt holds the default value on creation for the created_at field.
	nodedefinition.DefaultCreatedAt = nodedefinitionDescCreatedAt.Default.(func() time.Time)
	// nodedefinitionDescUpdatedAt is the schema descriptor for updated_at field.
	nodedefinitionDescUpdatedAt := nodedefinitionFields[18].Descriptor()
	// nodedefinition.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	nodedefinition.DefaultUpdatedAt = nodedefinitionDescUpdatedAt.Default.(func() time.Time)
	pluginFields := schema.Plugin{}.Fields()
	_ = pluginFields
	// pluginDescCreatedAt is the schema descriptor for created_at field.
	pluginDescCreatedAt := pluginFields[10].Descriptor()
	// plugin.DefaultCreatedAt holds the default value on creation for the created_at field.
	plugin.DefaultCreatedAt = pluginDescCreatedAt.Default.(func() time.Time)
	// pluginDescUpdatedAt is the schema descriptor for updated_at field.
	pluginDescUpdatedAt := pluginFields[11].Descriptor()
	// plugin.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	plugin.DefaultUpdatedAt = pluginDescUpdatedAt.Default.(func() time.Time)
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescUsername is the schema descriptor for username field.
	userDescUsername := userFields[1].Descriptor()
	// user.UsernameValidator is a validator for the "username" field. It is called by the builders before save.
	user.UsernameValidator = userDescUsername.Validators[0].(func(string) error)
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userFields[5].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userFields[6].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	workflowFields := schema.Workflow{}.Fields()
	_ = workflowFields
	// workflowDescCreatedAt is the schema descriptor for created_at field.
	workflowDescCreatedAt := workflowFields[8].Descriptor()
	// workflow.DefaultCreatedAt holds the default value on creation for the created_at field.
	workflow.DefaultCreatedAt = workflowDescCreatedAt.Default.(func() time.Time)
	// workflowDescUpdatedAt is the schema descriptor for updated_at field.
	workflowDescUpdatedAt := workflowFields[9].Descriptor()
	// workflow.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workflow.DefaultUpdatedAt = workflowDescUpdatedAt.Default.(func() time.Time)
	workflownodeFields := schema.WorkflowNode{}.Fields()
	_ = workflownodeFields
	// workflownodeDescCreatedAt is the schema descriptor for created_at field.
	workflownodeDescCreatedAt := workflownodeFields[16].Descriptor()
	// workflownode.DefaultCreatedAt holds the default value on creation for the created_at field.
	workflownode.DefaultCreatedAt = workflownodeDescCreatedAt.Default.(func() time.Time)
	// workflownodeDescUpdatedAt is the schema descriptor for updated_at field.
	workflownodeDescUpdatedAt := workflownodeFields[17].Descriptor()
	// workflownode.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workflownode.DefaultUpdatedAt = workflownodeDescUpdatedAt.Default.(func() time.Time)
	workflowsedgeFields := schema.WorkflowsEdge{}.Fields()
	_ = workflowsedgeFields
	// workflowsedgeDescCreatedAt is the schema descriptor for created_at field.
	workflowsedgeDescCreatedAt := workflowsedgeFields[7].Descriptor()
	// workflowsedge.DefaultCreatedAt holds the default value on creation for the created_at field.
	workflowsedge.DefaultCreatedAt = workflowsedgeDescCreatedAt.Default.(func() time.Time)
	// workflowsedgeDescUpdatedAt is the schema descriptor for updated_at field.
	workflowsedgeDescUpdatedAt := workflowsedgeFields[8].Descriptor()
	// workflowsedge.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workflowsedge.DefaultUpdatedAt = workflowsedgeDescUpdatedAt.Default.(func() time.Time)
	// workflowsedgeDescID is the schema descriptor for id field.
	workflowsedgeDescID := workflowsedgeFields[0].Descriptor()
	// workflowsedge.DefaultID holds the default value on creation for the id field.
	workflowsedge.DefaultID = workflowsedgeDescID.Default.(func() uuid.UUID)
}
